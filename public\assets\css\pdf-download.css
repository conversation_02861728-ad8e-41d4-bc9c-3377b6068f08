/**
 * Portal Judiciar - Stiluri pentru descărcarea PDF
 */

/* Stiluri pentru butonul de descărcare PDF */
#downloadPdfBtn {
    transition: all 0.3s ease;
}

#downloadPdfBtn:hover {
    background-color: #218838;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
}

#downloadPdfBtn:active {
    transform: translateY(1px);
}

#downloadPdfBtn.loading {
    pointer-events: none;
    opacity: 0.8;
}

/* Stiluri pentru indicatorul de încărcare */
.spinner-border-sm {
    width: 1rem;
    height: 1rem;
    border-width: 0.2em;
}

/* Stiluri pentru notificări */
.pdf-notification {
    position: fixed;
    top: 20px;
    right: 20px;
    z-index: 9999;
    padding: 15px 20px;
    border-radius: 4px;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
    animation: slideIn 0.3s ease-out forwards;
    max-width: 350px;
}

.pdf-notification.success {
    background-color: #d4edda;
    color: #155724;
    border-left: 4px solid #28a745;
}

.pdf-notification.error {
    background-color: #f8d7da;
    color: #721c24;
    border-left: 4px solid #dc3545;
}

.pdf-notification.info {
    background-color: #d1ecf1;
    color: #0c5460;
    border-left: 4px solid #17a2b8;
}

.pdf-notification-close {
    position: absolute;
    top: 5px;
    right: 5px;
    cursor: pointer;
    font-size: 16px;
    color: inherit;
    opacity: 0.7;
}

.pdf-notification-close:hover {
    opacity: 1;
}

@keyframes slideIn {
    from {
        transform: translateX(100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

@keyframes fadeOut {
    from {
        opacity: 1;
    }
    to {
        opacity: 0;
    }
}

.pdf-notification.fade-out {
    animation: fadeOut 0.5s ease-out forwards;
}
