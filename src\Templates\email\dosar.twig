<!DOCTYPE html>
<html lang="ro">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Detalii dosar {{ dosar.numar }}</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            line-height: 1.6;
            color: #333;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        
        h1, h2, h3 {
            color: #2c3e50;
        }
        
        table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 20px;
        }
        
        th, td {
            padding: 10px;
            border: 1px solid #ddd;
            text-align: left;
        }
        
        th {
            background-color: #f8f9fa;
            font-weight: bold;
        }
        
        .header {
            background-color: #2c3e50;
            color: white;
            padding: 20px;
            margin-bottom: 20px;
            border-radius: 5px;
        }
        
        .footer {
            margin-top: 30px;
            padding-top: 20px;
            border-top: 1px solid #ddd;
            font-size: 12px;
            color: #777;
        }
        
        .section {
            margin-bottom: 30px;
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>Detalii dosar {{ dosar.numar }}</h1>
    </div>
    
    {% if message %}
        <div class="section">
            <p>{{ message }}</p>
        </div>
    {% endif %}
    
    <div class="section">
        <h2>Informații dosar</h2>
        <table>
            <tr>
                <th>Număr dosar</th>
                <td>{{ dosar.numar }}</td>
            </tr>
            {% if dosar.numarVechi %}
                <tr>
                    <th>Număr vechi</th>
                    <td>{{ dosar.numarVechi }}</td>
                </tr>
            {% endif %}
            <tr>
                <th>Data înregistrării</th>
                <td>{{ dosar.data }}</td>
            </tr>
            <tr>
                <th>Instanță</th>
                <td>{{ instante[dosar.institutie] ?? dosar.institutie }}</td>
            </tr>
            {% if dosar.departament %}
                <tr>
                    <th>Departament</th>
                    <td>{{ dosar.departament }}</td>
                </tr>
            {% endif %}
            {% if dosar.categorieCazNume %}
                <tr>
                    <th>Categorie caz</th>
                    <td>{{ dosar.categorieCazNume }}</td>
                </tr>
            {% endif %}
            {% if dosar.stadiuProcesualNume %}
                <tr>
                    <th>Stadiu procesual</th>
                    <td>{{ dosar.stadiuProcesualNume }}</td>
                </tr>
            {% endif %}
            {% if dosar.obiect %}
                <tr>
                    <th>Obiect</th>
                    <td>{{ dosar.obiect }}</td>
                </tr>
            {% endif %}
            <tr>
                <th>Data ultimei modificări</th>
                <td>{{ dosar.dataModificare }}</td>
            </tr>
        </table>
    </div>
    
    {% if dosar.parti|length > 0 %}
        <div class="section">
            <h2>Părți implicate</h2>
            <table>
                <thead>
                    <tr>
                        <th>Nume</th>
                        <th>Calitate</th>
                    </tr>
                </thead>
                <tbody>
                    {% for parte in dosar.parti %}
                        <tr>
                            <td>{{ parte.nume }}</td>
                            <td>{{ parte.calitate }}</td>
                        </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
    {% endif %}
    
    {% if dosar.sedinte|length > 0 %}
        <div class="section">
            <h2>Ședințe de judecată</h2>
            <table>
                <thead>
                    <tr>
                        <th>Data</th>
                        <th>Ora</th>
                        <th>Complet</th>
                        <th>Soluție</th>
                    </tr>
                </thead>
                <tbody>
                    {% for sedinta in dosar.sedinte %}
                        <tr>
                            <td>{{ sedinta.data }}</td>
                            <td>{{ sedinta.ora ?: '-' }}</td>
                            <td>{{ sedinta.complet ?: '-' }}</td>
                            <td>{{ sedinta.solutie ?: '-' }}</td>
                        </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
    {% endif %}
    
    {% if dosar.caiAtac|length > 0 %}
        <div class="section">
            <h2>Căi de atac</h2>
            <table>
                <thead>
                    <tr>
                        <th>Data declarare</th>
                        <th>Tip cale atac</th>
                        <th>Parte declaratoare</th>
                        <th>Dosar instanță superioară</th>
                    </tr>
                </thead>
                <tbody>
                    {% for caleAtac in dosar.caiAtac %}
                        <tr>
                            <td>{{ caleAtac.dataDeclarare }}</td>
                            <td>{{ caleAtac.tipCaleAtac ?: '-' }}</td>
                            <td>{{ caleAtac.parteDeclaratoare ?: '-' }}</td>
                            <td>{{ caleAtac.numarDosarInstantaSuperior ?: '-' }}</td>
                        </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
    {% endif %}
    
    <div class="footer">
        <p>Acest email a fost trimis de {{ sender }} prin intermediul Portal Judiciar.</p>
        <p>Informațiile din acest email sunt preluate din portalul instanțelor de judecată și pot fi incomplete sau inexacte. Pentru informații complete și actualizate, vă rugăm să consultați portalul oficial al instanțelor de judecată.</p>
    </div>
</body>
</html>
