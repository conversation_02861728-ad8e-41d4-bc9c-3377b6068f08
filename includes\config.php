<?php
/**
 * Configurații generale pentru aplicație
 */

// Numele aplicației
define('APP_NAME', 'Portal Dosare Judecătorești');

// Setări pentru API-ul SOAP
define('SOAP_WSDL', 'http://portalquery.just.ro/query.asmx?WSDL');
define('SOAP_ENDPOINT', 'http://portalquery.just.ro/query.asmx');
define('SOAP_NAMESPACE', 'portalquery.just.ro');

// Setări pentru afișare
define('RESULTS_PER_PAGE', 25);

// Setări pentru formatare dată
define('DATE_FORMAT', 'd.m.Y');
define('DATETIME_FORMAT', 'd.m.Y H:i');

// Setări pentru afișarea erorilor (dezactivați în producție)
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

/**
 * Obține lista instanțelor judecătorești
 * Această funcție este acum definită în includes/functions.php
 * Această referință este menținută doar pentru documentație
 */
// Funcția getInstanteList() este definită în functions.php

/**
 * Funcție pentru afișarea erorilor în modul debug
 *
 * @param mixed $data Datele de afișat
 * @return void
 */
function debug($data) {
    echo '<pre>';
    print_r($data);
    echo '</pre>';
}