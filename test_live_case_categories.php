<?php
/**
 * Live testing of case category filtering with actual search functionality
 */

require_once 'includes/config.php';
require_once 'includes/functions.php';
require_once 'src/Services/DosarService.php';

echo "<h1>🔍 Live Case Category Filtering Tests</h1>\n";
echo "<style>
    .test-section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
    .success { background-color: #d4edda; border-color: #c3e6cb; }
    .error { background-color: #f8d7da; border-color: #f5c6cb; }
    .warning { background-color: #fff3cd; border-color: #ffeaa7; }
    .info { background-color: #d1ecf1; border-color: #bee5eb; }
    .test-result { margin: 5px 0; padding: 8px; border-radius: 3px; }
    .pass { background-color: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
    .fail { background-color: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
    .enhanced { background-color: #e7f3ff; border: 1px solid #b3d9ff; color: #0056b3; }
    .basic { background-color: #f8f9fa; border: 1px solid #dee2e6; color: #495057; }
    .needs-enhancement { background-color: #fff3cd; border: 1px solid #ffeaa7; color: #856404; }
    .search-url { background: #f8f9fa; padding: 10px; border-radius: 3px; margin: 10px 0; border-left: 4px solid #007bff; }
    .category-stats { background: #e9ecef; padding: 8px; margin: 5px 0; border-radius: 3px; }
</style>\n";

echo "<div class='test-section info'>";
echo "<h2>🎯 Live Search Testing for All Case Categories</h2>\n";
echo "<p><strong>Testing actual search functionality with each case category filter</strong></p>";
echo "</div>";

// Define all case categories for testing
$testCategories = [
    'civil' => 'Civil Law',
    'penal' => 'Criminal Law', 
    'comercial' => 'Commercial Law',
    'administrativ' => 'Administrative Law',
    'fiscal' => 'Tax Law',
    'munca' => 'Labor Law (Enhanced)',
    'familie' => 'Family Law',
    'contencios_administrativ' => 'Administrative Litigation (Enhanced)'
];

try {
    $dosarService = new \App\Services\DosarService();
    
    echo "<div class='test-section'>";
    echo "<h2>Live Search Tests by Category</h2>\n";
    
    $totalTests = 0;
    $successfulTests = 0;
    $enhancedCategories = 0;
    $basicCategories = 0;
    
    foreach ($testCategories as $categoryValue => $categoryDescription) {
        $totalTests++;
        $isEnhanced = strpos($categoryDescription, 'Enhanced') !== false;
        
        if ($isEnhanced) {
            $enhancedCategories++;
        } else {
            $basicCategories++;
        }
        
        echo "<h4>Testing Category: {$categoryValue} ({$categoryDescription})</h4>\n";
        
        // Prepare search parameters
        $searchParams = [
            'numarDosar' => '',
            'numeParte' => '',
            'obiectDosar' => '',
            'institutie' => null,
            'categorieInstanta' => '',
            'categorieCaz' => $categoryValue,
            'dataStart' => '01.01.2023',
            'dataStop' => '31.12.2023',
            'dataUltimaModificareStart' => '',
            'dataUltimaModificareStop' => '',
            '_maxResults' => 10
        ];
        
        echo "<div class='search-url'>";
        echo "<strong>Search Parameters:</strong><br>";
        echo "Category: {$categoryValue}<br>";
        echo "Date Range: 01.01.2023 - 31.12.2023<br>";
        echo "Max Results: 10";
        echo "</div>";
        
        try {
            $searchStart = microtime(true);
            $results = $dosarService->cautareAvansata($searchParams);
            $searchDuration = microtime(true) - $searchStart;
            
            $successfulTests++;
            
            echo "<div class='test-result " . ($isEnhanced ? 'enhanced' : 'basic') . "'>";
            echo "✅ Search completed: " . count($results) . " results in " . round($searchDuration, 3) . "s";
            echo "</div>";
            
            if (!empty($results)) {
                // Analyze the results to see if they match the category
                $categoryMatches = 0;
                $totalAnalyzed = min(5, count($results)); // Analyze first 5 results
                
                for ($i = 0; $i < $totalAnalyzed; $i++) {
                    $result = $results[$i];
                    $categorieCaz = strtolower($result->categorieCaz ?? '');
                    $categorieCazNume = strtolower($result->categorieCazNume ?? '');
                    $obiect = strtolower($result->obiect ?? '');
                    
                    $matches = false;
                    
                    // Test if the result matches the category filter
                    if ($isEnhanced) {
                        if ($categoryValue === 'munca') {
                            $laborTerms = ['munca', 'muncă', 'salarial', 'angajat', 'contract de munca'];
                            foreach ($laborTerms as $term) {
                                if (stripos($categorieCaz, $term) !== false ||
                                    stripos($categorieCazNume, $term) !== false ||
                                    stripos($obiect, $term) !== false) {
                                    $matches = true;
                                    break;
                                }
                            }
                        } elseif ($categoryValue === 'contencios_administrativ') {
                            $adminTerms = ['contencios', 'administrativ', 'act administrativ'];
                            foreach ($adminTerms as $term) {
                                if (stripos($categorieCaz, $term) !== false ||
                                    stripos($categorieCazNume, $term) !== false ||
                                    stripos($obiect, $term) !== false) {
                                    $matches = true;
                                    break;
                                }
                            }
                        }
                    } else {
                        // Basic string matching
                        $matches = stripos($categorieCaz, $categoryValue) !== false ||
                                  stripos($categorieCazNume, $categoryValue) !== false ||
                                  stripos($obiect, $categoryValue) !== false;
                    }
                    
                    if ($matches) {
                        $categoryMatches++;
                    }
                }
                
                $matchPercentage = ($categoryMatches / $totalAnalyzed) * 100;
                
                echo "<div class='category-stats'>";
                echo "📊 Category Match Analysis: {$categoryMatches}/{$totalAnalyzed} results match ({$matchPercentage}%)";
                echo "</div>";
                
                // Show sample results
                echo "<h5>Sample Results:</h5>";
                for ($i = 0; $i < min(3, count($results)); $i++) {
                    $result = $results[$i];
                    echo "<div class='test-result info'>";
                    echo "Case: " . htmlspecialchars($result->numar ?? 'N/A');
                    echo " | Category: " . htmlspecialchars($result->categorieCaz ?? 'N/A');
                    echo " | Date: " . htmlspecialchars($result->data ?? 'N/A');
                    echo "</div>";
                }
                
                if ($matchPercentage < 50) {
                    echo "<div class='test-result needs-enhancement'>";
                    echo "⚠️ Low category match rate - may need enhanced terminology matching";
                    echo "</div>";
                }
                
            } else {
                echo "<div class='test-result warning'>";
                echo "⚠️ No results found - this may be expected if no cases exist for this category in 2023";
                echo "</div>";
            }
            
        } catch (Exception $e) {
            echo "<div class='test-result fail'>";
            echo "❌ Search failed: " . htmlspecialchars($e->getMessage());
            echo "</div>";
        }
        
        echo "<hr style='margin: 20px 0; border: 1px solid #dee2e6;'>";
    }
    
    echo "</div>";
    
    // Summary and Recommendations
    echo "<div class='test-section success'>";
    echo "<h2>📊 Testing Summary & Recommendations</h2>\n";
    
    echo "<div class='category-stats'>";
    echo "<strong>Test Results Summary:</strong><br>";
    echo "Total Categories Tested: {$totalTests}<br>";
    echo "Successful Tests: {$successfulTests}<br>";
    echo "Enhanced Categories: {$enhancedCategories}<br>";
    echo "Basic Categories: {$basicCategories}<br>";
    echo "Success Rate: " . round(($successfulTests / $totalTests) * 100, 1) . "%";
    echo "</div>";
    
    echo "<h4>🔧 Enhancement Recommendations:</h4>";
    
    $recommendationsForEnhancement = [
        'civil' => [
            'priority' => 'High',
            'terms' => ['civil', 'civilă', 'drept civil', 'proces civil', 'litigiu civil', 'contracte civile', 'răspundere civilă', 'daune civile']
        ],
        'penal' => [
            'priority' => 'High', 
            'terms' => ['penal', 'penală', 'drept penal', 'infracțiune', 'crimă', 'delict', 'proces penal', 'urmărire penală']
        ],
        'comercial' => [
            'priority' => 'Medium',
            'terms' => ['comercial', 'comercială', 'drept comercial', 'societate comercială', 'afaceri', 'comerț', 'întreprindere']
        ],
        'fiscal' => [
            'priority' => 'Medium',
            'terms' => ['fiscal', 'fiscală', 'drept fiscal', 'impozit', 'taxe', 'contribuții', 'ANAF', 'fisc']
        ],
        'familie' => [
            'priority' => 'Medium',
            'terms' => ['familie', 'familii', 'drept de familie', 'divorț', 'căsătorie', 'custodie', 'întreținere', 'adopție']
        ]
    ];
    
    foreach ($recommendationsForEnhancement as $category => $recommendation) {
        echo "<div class='test-result needs-enhancement'>";
        echo "<strong>{$category}</strong> - Priority: {$recommendation['priority']}<br>";
        echo "Suggested terms: " . implode(', ', array_slice($recommendation['terms'], 0, 5)) . "...";
        echo "</div>";
    }
    
    echo "</div>";
    
} catch (Exception $e) {
    echo "<div class='test-section error'>";
    echo "<h2>❌ Critical Error</h2>\n";
    echo "<div class='test-result fail'>";
    echo "Error: " . htmlspecialchars($e->getMessage());
    echo "</div>";
    echo "</div>";
}

echo "<div class='test-section info'>";
echo "<h2>🏁 Live Case Category Testing Complete</h2>\n";
echo "<p><strong>All case categories have been tested with actual search functionality.</strong></p>";
echo "<p><strong>Next:</strong> Implement enhanced terminology matching for high-priority categories.</p>";
echo "</div>";
?>
