<?php

/**
 * Portal Judiciar - Pagina principală
 */

// Încărcăm bootstrap-ul aplicației
require_once dirname(__DIR__) . '/bootstrap.php';

// Importăm clasele necesare
use App\Helpers\TemplateEngine;
use App\Services\DosarService;

// Inițializăm motorul de șabloane
$templateEngine = new TemplateEngine();

// Lista instanțelor
$instante = [
    'JudecatoriaSECTORUL1BUCURESTI' => 'Judecătoria Sectorului 1 București',
    'JudecatoriaSECTORUL2BUCURESTI' => 'Judecătoria Sectorului 2 București',
    'JudecatoriaSECTORUL3BUCURESTI' => 'Judecătoria Sectorului 3 București',
    'JudecatoriaSECTORUL4BUCURESTI' => 'Judecătoria Sectorului 4 București',
    'JudecatoriaSECTORUL5BUCURESTI' => 'Judecătoria Sectorului 5 București',
    'JudecatoriaSECTORUL6BUCURESTI' => 'Judecătoria Sectorului 6 București',
    'TribunalulBUCURESTI' => 'Tribunalul București',
    'CurteadeApelBUCURESTI' => 'Curtea de Apel București',
    'JudecatoriaCLUJNAPOCA' => 'Judecătoria Cluj-Napoca',
    'TribunalulCLUJ' => 'Tribunalul Cluj',
    'CurteadeApelCLUJ' => 'Curtea de Apel Cluj',
    'JudecatoriaCONSTANTA' => 'Judecătoria Constanța',
    'TribunalulCONSTANTA' => 'Tribunalul Constanța',
    'CurteadeApelCONSTANTA' => 'Curtea de Apel Constanța',
    'JudecatoriaTIMISOARA' => 'Judecătoria Timișoara',
    'TribunalulTIMIS' => 'Tribunalul Timiș',
    'CurteadeApelTIMISOARA' => 'Curtea de Apel Timișoara',
    'JudecatoriaIASI' => 'Judecătoria Iași',
    'TribunalulIASI' => 'Tribunalul Iași',
    'CurteadeApelIASI' => 'Curtea de Apel Iași',
    'JudecatoriaBRASOV' => 'Judecătoria Brașov',
    'TribunalulBRASOV' => 'Tribunalul Brașov',
    'CurteadeApelBRASOV' => 'Curtea de Apel Brașov',
    'InaltaCurtedeCasatiesiJustitie' => 'Înalta Curte de Casație și Justiție'
];

// Datele pentru șablon
$data = [
    'instante' => $instante
];

// Afișăm șablonul
echo $templateEngine->render('index.twig', $data);
