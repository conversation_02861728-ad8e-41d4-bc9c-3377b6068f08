<?php
/**
 * Clasa pentru interacțiunea cu API-ul SOAP al Portalului Instanțelor de Judecată
 */
class PortalSoapClient {
    /**
     * Instanța clientului SOAP
     * @var SoapClient
     */
    private $client;
    
    /**
     * Constructor
     */
    public function __construct() {
        try {
            // Opțiuni pentru clientul SOAP
            $options = [
                'soap_version' => SOAP_1_2,
                'exceptions' => true,
                'trace' => true,
                'cache_wsdl' => WSDL_CACHE_NONE
            ];
            
            // Inițializare client SOAP
            $this->client = new SoapClient(SOAP_WSDL, $options);
        } catch (SoapFault $e) {
            throw new Exception('Eroare la inițializarea clientului SOAP: ' . $e->getMessage());
        }
    }
    
    /**
     * Caută dosare în funcție de parametrii specificați
     * 
     * @param array $params Parametrii de căutare
     * @return array Rezultatele căutării
     */
    public function searchDosare($params) {
        try {
            // Parametrii pentru căutare
            $searchParams = [
                'numarDosar' => isset($params['numarDosar']) ? $params['numarDosar'] : '',
                'obiectDosar' => isset($params['obiectDosar']) ? $params['obiectDosar'] : '',
                'numeParte' => isset($params['numeParte']) ? $params['numeParte'] : '',
                'institutie' => isset($params['institutie']) ? $params['institutie'] : '',
                'dataStart' => isset($params['dataStart']) ? $this->formatDateForSoap($params['dataStart']) : null,
                'dataStop' => isset($params['dataStop']) ? $this->formatDateForSoap($params['dataStop']) : null,
                'dataUltimaModificareStart' => isset($params['dataUltimaModificareStart']) ? $this->formatDateForSoap($params['dataUltimaModificareStart']) : null,
                'dataUltimaModificareStop' => isset($params['dataUltimaModificareStop']) ? $this->formatDateForSoap($params['dataUltimaModificareStop']) : null
            ];
            
            // Apelare metodă SOAP
            $result = $this->client->CautareDosare2($searchParams);
            
            // Procesare rezultat
            if (isset($result->CautareDosare2Result->Dosar)) {
                $dosare = $result->CautareDosare2Result->Dosar;
                
                // Verifică dacă rezultatul este un singur dosar sau un array de dosare
                if (!is_array($dosare)) {
                    $dosare = [$dosare];
                }
                
                return $dosare;
            }
            
            return [];
        } catch (SoapFault $e) {
            throw new Exception('Eroare la căutarea dosarelor: ' . $e->getMessage());
        }
    }
    
    /**
     * Obține detalii despre un dosar specific
     * 
     * @param string $numarDosar Numărul dosarului
     * @param string $institutie Instituția
     * @return object Detaliile dosarului
     */
    public function getDosar($numarDosar, $institutie) {
        try {
            // Parametrii pentru căutare
            $searchParams = [
                'numarDosar' => $numarDosar,
                'institutie' => $institutie,
                'obiectDosar' => '',
                'numeParte' => '',
                'dataStart' => null,
                'dataStop' => null,
                'dataUltimaModificareStart' => null,
                'dataUltimaModificareStop' => null
            ];
            
            // Apelare metodă SOAP
            $result = $this->client->CautareDosare2($searchParams);
            
            // Procesare rezultat
            if (isset($result->CautareDosare2Result->Dosar)) {
                $dosare = $result->CautareDosare2Result->Dosar;
                
                // Verifică dacă rezultatul este un singur dosar sau un array de dosare
                if (!is_array($dosare)) {
                    return $dosare;
                } else {
                    // Caută dosarul specific în rezultate
                    foreach ($dosare as $dosar) {
                        if ($dosar->numar === $numarDosar && $dosar->institutie === $institutie) {
                            return $dosar;
                        }
                    }
                }
            }
            
            return null;
        } catch (SoapFault $e) {
            throw new Exception('Eroare la obținerea detaliilor dosarului: ' . $e->getMessage());
        }
    }
    
    /**
     * Formatează o dată pentru a fi utilizată în cererea SOAP
     * 
     * @param string $date Data în format string (d.m.Y)
     * @return string Data formatată pentru SOAP
     */
    private function formatDateForSoap($date) {
        if (empty($date)) {
            return null;
        }
        
        $dateObj = DateTime::createFromFormat('d.m.Y', $date);
        
        if ($dateObj) {
            return $dateObj->format('Y-m-d\TH:i:s');
        }
        
        return null;
    }
}
