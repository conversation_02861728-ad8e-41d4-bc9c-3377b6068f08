<?php
/**
 * Pagina principală cu formularul de căutare
 */

// Includere fișiere necesare
require_once 'includes/config.php';
require_once 'includes/functions.php';
require_once 'includes/header.php';
?>

<!-- Font Awesome pentru iconițe -->
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css">

<!-- Flatpickr CSS -->
<link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/flatpickr@4.6.13/dist/flatpickr.min.css">
<link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/flatpickr@4.6.13/dist/themes/material_blue.css">

<!-- Stiluri specifice pentru pagina principală -->
<style>
    /* Stiluri de bază îmbunătățite */
    .search-card {
        border: none;
        border-radius: 8px;
        box-shadow: 0 4px 15px rgba(0, 0, 0, 0.08);
        transition: all 0.3s ease;
        overflow: hidden;
    }

    .search-card:hover {
        box-shadow: 0 8px 25px rgba(0, 0, 0, 0.12);
        transform: translateY(-2px);
    }

    .card-header {
        border-bottom: none;
        padding: 1.25rem 1.5rem;
    }

    .card-header h2 {
        font-size: 1.5rem;
        font-weight: 600;
        margin: 0;
        display: flex;
        align-items: center;
    }

    .card-header h2 i {
        margin-right: 0.75rem;
        font-size: 1.25rem;
    }

    .card-body {
        padding: 1.5rem;
    }

    /* Stiluri pentru formulare */
    .form-group label {
        font-weight: 600;
        color: var(--gray-700, #495057);
        margin-bottom: 0.5rem;
        display: block;
    }

    .form-control {
        border-radius: 6px;
        padding: 0.75rem;
        border: 1px solid var(--gray-300, #dee2e6);
        transition: all 0.3s ease;
        font-size: 1rem;
        min-height: 44px;
    }

    .form-control:focus {
        border-color: var(--secondary-color, #3498db);
        box-shadow: 0 0 0 0.2rem rgba(52, 152, 219, 0.25);
    }

    .input-group-text {
        background-color: var(--gray-100, #f8f9fa);
        border-color: var(--gray-300, #dee2e6);
        color: var(--gray-700, #495057);
        min-height: 44px;
    }

    .advanced-filters {
        background-color: var(--gray-100, #f8f9fa);
        border-radius: 8px;
        padding: 1.25rem;
        margin-top: 1.25rem;
        border: 1px solid var(--gray-200, #e9ecef);
        transition: all 0.3s ease;
    }

    /* Stiluri pentru tooltip-ul de informare */
    .search-tooltip {
        background-color: rgba(0, 123, 255, 0.1);
        border-left: 3px solid rgba(0, 123, 255, 0.7);
        padding: 8px 12px;
        margin-bottom: 10px;
        border-radius: 4px;
        font-size: 0.9rem;
        color: #495057;
        transition: opacity 0.5s ease;
        opacity: 1;
    }

    /* Stilurile pentru butoane au fost mutate în fișierul assets/css/buttons.css */

    /* Stiluri pentru carduri informative */
    .info-card {
        border: none;
        border-radius: 8px;
        box-shadow: 0 4px 15px rgba(0, 0, 0, 0.08);
        overflow: hidden;
    }

    .info-icon {
        font-size: 2.5rem;
        color: var(--info-color, #17a2b8);
        margin-bottom: 1rem;
        transition: all 0.3s ease;
    }

    .card:hover .info-icon {
        transform: scale(1.1);
    }

    /* Stiluri pentru loading indicator */
    .loading {
        display: none;
        text-align: center;
        padding: 1.5rem;
        position: relative;
        margin: 1.5rem auto;
        max-width: 300px;
        background-color: rgba(52, 152, 219, 0.05);
        border-radius: 8px;
        box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
        transition: all 0.3s ease;
    }

    .loading.show {
        display: block;
        animation: fadeIn 0.3s ease;
    }

    .loading-spinner {
        width: 48px;
        height: 48px;
        margin: 0 auto;
        border: 4px solid var(--gray-200, #e9ecef);
        border-top: 4px solid var(--secondary-color, #3498db);
        border-radius: 50%;
        animation: spin 1s linear infinite;
        box-shadow: 0 0 10px rgba(52, 152, 219, 0.2);
    }

    .loading p {
        margin-top: 1rem;
        font-weight: 500;
        color: var(--gray-700, #495057);
    }

    @keyframes spin {
        0% { transform: rotate(0deg); }
        100% { transform: rotate(360deg); }
    }

    @keyframes fadeIn {
        from { opacity: 0; transform: translateY(10px); }
        to { opacity: 1; transform: translateY(0); }
    }

    /* Stiluri pentru Flatpickr */
    .flatpickr-container {
        position: relative;
        width: 0;
        height: 0;
        overflow: visible;
    }

    .flatpickr-calendar {
        background: #fff;
        box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
        border-radius: 8px;
        width: 307.875px !important;
        position: absolute;
        z-index: 9999 !important;
        visibility: hidden;
        opacity: 0;
        transition: opacity 0.3s ease, transform 0.3s ease;
        display: none;
        transform: translateY(10px);
    }

    .flatpickr-calendar.open {
        visibility: visible;
        opacity: 1;
        display: block;
        transform: translateY(0);
    }

    /* Stiluri pentru elemente de card */
    .card-deck .card {
        transition: all 0.3s ease;
        margin-bottom: 1rem;
    }

    .card-deck .card:hover {
        transform: translateY(-5px);
        box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
    }

    /* Media queries pentru responsive design */
    @media (max-width: 991.98px) {
        .card-deck {
            display: flex;
            flex-direction: column;
        }

        .card-deck .card {
            margin-bottom: 1rem;
        }
    }

    @media (max-width: 767.98px) {
        .btn-search {
            width: 100%;
            margin-bottom: 0.75rem;
        }

        .card-header h2 {
            font-size: 1.35rem;
        }

        .flatpickr-calendar {
            width: 100% !important;
            max-width: 307.875px;
        }

        .card-body {
            padding: 1.25rem;
        }
    }

    @media (max-width: 575.98px) {
        .card-header h2 {
            font-size: 1.25rem;
        }

        .card-body {
            padding: 1rem;
        }

        .info-icon {
            font-size: 2rem;
        }

        /* Stiluri îmbunătățite pentru loading indicator pe mobile */
        .loading {
            max-width: 100%;
            margin: 1rem 0;
            padding: 1rem;
        }

        .loading-spinner {
            width: 40px;
            height: 40px;
        }

        .loading p {
            font-size: 0.95rem;
        }
    }

    /* Stiluri pentru tooltip */
    .search-tooltip {
        background-color: rgba(0, 123, 255, 0.1);
        border-left: 3px solid rgba(0, 123, 255, 0.7);
        padding: 8px 12px;
        margin-top: 10px;
        margin-bottom: 10px;
        border-radius: 4px;
        font-size: 0.9rem;
        color: #495057;
        transition: opacity 0.5s ease;
        opacity: 1;
    }

    /* Stiluri pentru a asigura afișarea corectă a footer-ului */
    html, body {
        height: 100%;
    }

    body {
        display: flex;
        flex-direction: column;
        min-height: 100vh;
    }

    .content-wrapper {
        flex: 1 0 auto;
    }

    .modern-footer {
        flex-shrink: 0;
        margin-top: auto;
    }

    /* Stiluri pentru a asigura afișarea corectă a filtrelor avansate */
    #advancedFilters {
        overflow: hidden;
        transition: all 0.3s ease;
        max-height: 0;
    }

    #advancedFilters.show {
        max-height: 1000px; /* Valoare suficient de mare pentru a acomoda conținutul */
        padding: 1.25rem;
        margin-top: 1.25rem;
        border: 1px solid var(--gray-200, #e9ecef);
    }

    /* Stiluri îmbunătățite pentru dropdown-urile de instanțe */
    #institutie, #categorieInstanta {
        background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='m6 8 4 4 4-4'/%3e%3c/svg%3e");
        background-position: right 0.5rem center;
        background-repeat: no-repeat;
        background-size: 1.5em 1.5em;
        padding-right: 2.5rem;
        appearance: none;
        -webkit-appearance: none;
        -moz-appearance: none;
    }

    #institutie:focus, #categorieInstanta:focus {
        outline: none;
        border-color: var(--secondary-color, #3498db);
        box-shadow: 0 0 0 0.2rem rgba(52, 152, 219, 0.25);
    }

    /* Stiluri pentru opțiunile dropdown-ului */
    #institutie option, #categorieInstanta option {
        padding: 0.5rem;
        background-color: white;
        color: var(--gray-800, #343a40);
    }

    #institutie option:hover, #categorieInstanta option:hover {
        background-color: var(--gray-100, #f8f9fa);
    }

    /* Stiluri pentru indicatorul de categorie */
    .category-indicator {
        display: inline-block;
        width: 8px;
        height: 8px;
        border-radius: 50%;
        margin-right: 0.5rem;
        vertical-align: middle;
    }

    .category-indicator.curtea-suprema {
        background-color: #8b5cf6;
    }

    .category-indicator.curte-apel {
        background-color: #3b82f6;
    }

    .category-indicator.tribunal {
        background-color: #10b981;
    }

    .category-indicator.judecatorie {
        background-color: #f59e0b;
    }

    /* Navigation Bar Styles */
    .navbar {
        background-color: #f8f9fa !important;
        border-bottom: 2px solid #007bff;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        padding: 0.75rem 0;
    }

    .navbar-brand {
        font-weight: 600;
        color: #2c3e50 !important;
        font-size: 1.25rem;
        transition: all 0.3s ease;
    }

    .navbar-brand:hover {
        color: #007bff !important;
        transform: translateY(-1px);
    }

    .navbar-brand i {
        color: #007bff;
        margin-right: 0.5rem;
    }

    .nav-link {
        color: #495057 !important;
        font-weight: 500;
        padding: 0.5rem 1rem !important;
        border-radius: 6px;
        transition: all 0.3s ease;
        margin: 0 0.25rem;
    }

    .nav-link:hover {
        color: #007bff !important;
        background-color: rgba(0, 123, 255, 0.1);
        transform: translateY(-1px);
    }

    .nav-link.active,
    .nav-item.active .nav-link {
        color: #007bff !important;
        background-color: rgba(0, 123, 255, 0.15);
        font-weight: 600;
    }

    .nav-link i {
        margin-right: 0.5rem;
        width: 16px;
        text-align: center;
    }

    /* Responsive navigation */
    @media (max-width: 991.98px) {
        .navbar-nav {
            margin-top: 1rem;
            padding-top: 1rem;
            border-top: 1px solid #dee2e6;
        }

        .nav-link {
            margin: 0.25rem 0;
            text-align: center;
        }
    }

    @media (max-width: 575.98px) {
        .navbar-brand {
            font-size: 1.1rem;
        }

        .nav-link {
            padding: 0.75rem 1rem !important;
        }
    }
</style>

<div class="content-wrapper">
<!-- Navigation Bar -->
<nav class="navbar navbar-expand-lg navbar-light bg-light border-bottom">
    <div class="container">
        <a class="navbar-brand" href="index.php">
            <i class="fas fa-gavel me-2"></i>
            DosareJust.ro - Portal Judiciar
        </a>

        <button class="navbar-toggler" type="button" data-toggle="collapse" data-target="#navbarNav" aria-controls="navbarNav" aria-expanded="false" aria-label="Toggle navigation">
            <span class="navbar-toggler-icon"></span>
        </button>

        <div class="collapse navbar-collapse" id="navbarNav">
            <ul class="navbar-nav ml-auto">
                <li class="nav-item active">
                    <a class="nav-link" href="index.php">
                        <i class="fas fa-search me-1"></i>
                        Căutare avansată
                        <span class="sr-only">(current)</span>
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="index.php">
                        <i class="fas fa-search-plus me-1"></i>
                        Căutare Simplă
                    </a>
                </li>
            </ul>
        </div>
    </div>
</nav>

<div class="container mt-4">
    <div class="row">
        <div class="col-md-12">
            <div class="card search-card">
                <div class="card-header bg-primary text-white">
                    <h2><i class="fas fa-search mr-2"></i>Căutare dosare judecătorești</h2>
                </div>
                <div class="card-body">
                    <!-- Mesaj informativ -->
                    <div class="alert alert-info mb-4 d-flex align-items-center" role="alert">
                        <i class="fas fa-info-circle mr-3" style="font-size: 1.5rem;"></i>
                        <div>
                            <strong>Informație:</strong> Completați cel puțin unul dintre câmpurile de mai jos pentru a începe căutarea.
                        </div>
                    </div>

                    <form id="searchForm" action="search.php" method="GET" aria-label="Formular de căutare dosare">
                        <div class="row">
                            <div class="col-lg-4 col-md-6 mb-4">
                                <div class="form-group">
                                    <label for="numarDosar"><i class="fas fa-folder mr-1"></i> Număr dosar:</label>
                                    <div class="input-group">
                                        <div class="input-group-prepend">
                                            <span class="input-group-text"><i class="fas fa-hashtag"></i></span>
                                        </div>
                                        <input type="text" class="form-control" id="numarDosar" name="numarDosar" placeholder="Ex: 1234/123/2023" aria-describedby="numarDosarHelp" value="<?php echo isset($_GET['numarDosar']) ? htmlspecialchars($_GET['numarDosar']) : ''; ?>">
                                    </div>
                                    <small id="numarDosarHelp" class="form-text text-muted">Introduceți numărul complet al dosarului</small>
                                </div>
                            </div>
                            <div class="col-lg-4 col-md-6 mb-4">
                                <div class="form-group">
                                    <label for="numeParte"><i class="fas fa-user mr-1"></i> Nume parte:</label>
                                    <div class="input-group">
                                        <div class="input-group-prepend">
                                            <span class="input-group-text"><i class="fas fa-user"></i></span>
                                        </div>
                                        <input type="text" class="form-control" id="numeParte" name="numeParte" placeholder="Nume și prenume" aria-describedby="numeParteHelp" value="<?php echo isset($_GET['numeParte']) ? htmlspecialchars($_GET['numeParte']) : ''; ?>">
                                    </div>
                                    <small id="numeParteHelp" class="form-text text-muted">Introduceți numele complet sau parțial</small>
                                </div>
                            </div>
                            <div class="col-lg-4 col-md-12 mb-4">
                                <div class="form-group">
                                    <label for="obiectDosar"><i class="fas fa-gavel mr-1"></i> Obiect dosar:</label>
                                    <div class="input-group">
                                        <div class="input-group-prepend">
                                            <span class="input-group-text"><i class="fas fa-file-alt"></i></span>
                                        </div>
                                        <input type="text" class="form-control" id="obiectDosar" name="obiectDosar" placeholder="Ex: Divorț" aria-describedby="obiectDosarHelp" value="<?php echo isset($_GET['obiectDosar']) ? htmlspecialchars($_GET['obiectDosar']) : ''; ?>">
                                    </div>
                                    <small id="obiectDosarHelp" class="form-text text-muted">Introduceți obiectul dosarului</small>
                                </div>
                            </div>
                        </div>

                        <div class="mt-3">
                            <a href="#" id="advancedFiltersToggle" class="text-primary d-flex align-items-center justify-content-center">
                                <i class="fas fa-filter mr-2"></i>
                                <span>Arată filtrele avansate</span>
                                <i class="fas fa-chevron-down ml-2"></i>
                            </a>
                        </div>

                        <div id="advancedFilters" class="advanced-filters mt-3">
                            <h5 class="mb-3"><i class="fas fa-sliders-h mr-2"></i>Filtre avansate</h5>

                            <div class="row">
                                <div class="col-lg-4 col-md-6 mb-4">
                                    <div class="form-group">
                                        <label for="institutie"><i class="fas fa-university mr-1"></i> Instanță judecătorească:</label>
                                        <div class="input-group">
                                            <div class="input-group-prepend">
                                                <span class="input-group-text"><i class="fas fa-university"></i></span>
                                            </div>
                                            <select class="form-control" id="institutie" name="institutie" aria-describedby="institutieHelp">
                                                <option value="">-- Selectați instanța --</option>
                                                <?php
                                                // Obținem lista instanțelor
                                                $institutii = getInstanteList();

                                                // Obținem valoarea selectată anterior (pentru preservarea valorii)
                                                $selectedInstitutie = $_GET['institutie'] ?? '';

                                                // Verificăm dacă avem instanțe în listă
                                                if (!empty($institutii)) {
                                                    foreach ($institutii as $key => $value) {
                                                        $selected = ($key === $selectedInstitutie) ? ' selected' : '';
                                                        echo '<option value="' . htmlspecialchars($key) . '"' . $selected . '>' . htmlspecialchars($value) . '</option>';
                                                    }
                                                } else {
                                                    echo '<option value="" disabled>Nu sunt disponibile instanțe</option>';
                                                }
                                                ?>
                                            </select>
                                        </div>
                                        <small id="institutieHelp" class="form-text text-muted">
                                            <i class="fas fa-info-circle mr-1"></i>
                                            Selectați instanța pentru a filtra rezultatele după instanța judecătorească specifică
                                        </small>
                                    </div>
                                </div>
                                <div class="col-lg-4 col-md-6 mb-4">
                                    <div class="form-group">
                                        <label for="categorieInstanta"><i class="fas fa-filter mr-1"></i> Categorie instanță:</label>
                                        <div class="input-group">
                                            <div class="input-group-prepend">
                                                <span class="input-group-text"><i class="fas fa-filter"></i></span>
                                            </div>
                                            <select class="form-control" id="categorieInstanta" name="categorieInstanta" aria-describedby="categorieInstantaHelp">
                                                <option value="">-- Toate categoriile --</option>
                                                <option value="curtea_suprema" <?php echo (isset($_GET['categorieInstanta']) && $_GET['categorieInstanta'] === 'curtea_suprema') ? 'selected' : ''; ?>>Înalta Curte de Casație și Justiție</option>
                                                <option value="curte_apel" <?php echo (isset($_GET['categorieInstanta']) && $_GET['categorieInstanta'] === 'curte_apel') ? 'selected' : ''; ?>>Curți de Apel</option>
                                                <option value="tribunal" <?php echo (isset($_GET['categorieInstanta']) && $_GET['categorieInstanta'] === 'tribunal') ? 'selected' : ''; ?>>Tribunale</option>
                                                <option value="judecatorie" <?php echo (isset($_GET['categorieInstanta']) && $_GET['categorieInstanta'] === 'judecatorie') ? 'selected' : ''; ?>>Judecătorii</option>
                                            </select>
                                        </div>
                                        <small id="categorieInstantaHelp" class="form-text text-muted">
                                            <i class="fas fa-info-circle mr-1"></i>
                                            Filtrați după tipul de instanță pentru a restrânge opțiunile
                                        </small>
                                    </div>
                                </div>
                                <div class="col-lg-4 col-md-12 mb-4">
                                    <div class="form-group">
                                        <label for="categorieCaz"><i class="fas fa-tags mr-1"></i> Categorie caz:</label>
                                        <div class="input-group">
                                            <div class="input-group-prepend">
                                                <span class="input-group-text"><i class="fas fa-tags"></i></span>
                                            </div>
                                            <select class="form-control" id="categorieCaz" name="categorieCaz" aria-describedby="categorieCazHelp">
                                                <option value="">-- Selectați categoria --</option>
                                                <option value="civil" <?php echo (isset($_GET['categorieCaz']) && $_GET['categorieCaz'] === 'civil') ? 'selected' : ''; ?>>Civil</option>
                                                <option value="penal" <?php echo (isset($_GET['categorieCaz']) && $_GET['categorieCaz'] === 'penal') ? 'selected' : ''; ?>>Penal</option>
                                                <option value="comercial" <?php echo (isset($_GET['categorieCaz']) && $_GET['categorieCaz'] === 'comercial') ? 'selected' : ''; ?>>Comercial</option>
                                                <option value="administrativ" <?php echo (isset($_GET['categorieCaz']) && $_GET['categorieCaz'] === 'administrativ') ? 'selected' : ''; ?>>Administrativ</option>
                                                <option value="fiscal" <?php echo (isset($_GET['categorieCaz']) && $_GET['categorieCaz'] === 'fiscal') ? 'selected' : ''; ?>>Fiscal</option>
                                                <option value="munca" <?php echo (isset($_GET['categorieCaz']) && $_GET['categorieCaz'] === 'munca') ? 'selected' : ''; ?>>Muncă</option>
                                                <option value="familie" <?php echo (isset($_GET['categorieCaz']) && $_GET['categorieCaz'] === 'familie') ? 'selected' : ''; ?>>Familie</option>
                                                <option value="contencios_administrativ" <?php echo (isset($_GET['categorieCaz']) && $_GET['categorieCaz'] === 'contencios_administrativ') ? 'selected' : ''; ?>>Contencios administrativ</option>
                                            </select>
                                        </div>
                                        <small id="categorieCazHelp" class="form-text text-muted">
                                            <i class="fas fa-info-circle mr-1"></i>
                                            Selectați categoria cazului pentru a filtra după tipul de proces juridic
                                        </small>
                                    </div>
                                </div>
                            </div>

                            <div class="row">
                                <div class="col-lg-3 col-md-6 mb-4">
                                    <div class="form-group">
                                        <label for="dataStart"><i class="far fa-calendar-alt mr-1"></i> Data început:</label>
                                        <div class="input-group">
                                            <div class="input-group-prepend">
                                                <span class="input-group-text"><i class="far fa-calendar"></i></span>
                                            </div>
                                            <input type="text" class="form-control datepicker" id="dataStart" name="dataStart" placeholder="ZZ.LL.AAAA" autocomplete="off" value="<?php echo isset($_GET['dataStart']) ? htmlspecialchars($_GET['dataStart']) : ''; ?>">
                                        </div>
                                    </div>
                                </div>
                                <div class="col-lg-3 col-md-6 mb-4">
                                    <div class="form-group">
                                        <label for="dataStop"><i class="far fa-calendar-alt mr-1"></i> Data sfârșit:</label>
                                        <div class="input-group">
                                            <div class="input-group-prepend">
                                                <span class="input-group-text"><i class="far fa-calendar"></i></span>
                                            </div>
                                            <input type="text" class="form-control datepicker" id="dataStop" name="dataStop" placeholder="ZZ.LL.AAAA" autocomplete="off" value="<?php echo isset($_GET['dataStop']) ? htmlspecialchars($_GET['dataStop']) : ''; ?>">
                                        </div>
                                    </div>
                                </div>
                                <div class="col-lg-3 col-md-6 mb-4">
                                    <div class="form-group">
                                        <label for="dataUltimaModificareStart"><i class="fas fa-history mr-1"></i> Data modificării început:</label>
                                        <div class="input-group">
                                            <div class="input-group-prepend">
                                                <span class="input-group-text"><i class="far fa-calendar"></i></span>
                                            </div>
                                            <input type="text" class="form-control datepicker" id="dataUltimaModificareStart" name="dataUltimaModificareStart" placeholder="ZZ.LL.AAAA" autocomplete="off" value="<?php echo isset($_GET['dataUltimaModificareStart']) ? htmlspecialchars($_GET['dataUltimaModificareStart']) : ''; ?>">
                                        </div>
                                    </div>
                                </div>
                                <div class="col-lg-3 col-md-6 mb-4">
                                    <div class="form-group">
                                        <label for="dataUltimaModificareStop"><i class="fas fa-history mr-1"></i> Data modificării sfârșit:</label>
                                        <div class="input-group">
                                            <div class="input-group-prepend">
                                                <span class="input-group-text"><i class="far fa-calendar"></i></span>
                                            </div>
                                            <input type="text" class="form-control datepicker" id="dataUltimaModificareStop" name="dataUltimaModificareStop" placeholder="ZZ.LL.AAAA" autocomplete="off" value="<?php echo isset($_GET['dataUltimaModificareStop']) ? htmlspecialchars($_GET['dataUltimaModificareStop']) : ''; ?>">
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Indicator de încărcare -->
                        <div id="loading" class="loading" role="status" aria-live="polite" aria-atomic="true">
                            <div class="loading-spinner" aria-hidden="true"></div>
                            <p class="mt-2">Se încarcă rezultatele, vă rugăm așteptați...</p>
                        </div>

                        <div class="mt-4 button-container">
                            <button type="submit" class="btn btn-primary btn-search" aria-label="Caută dosare" aria-live="polite">
                                <i class="fas fa-search mr-2" aria-hidden="true"></i> Caută dosare
                            </button>
                            <button type="reset" class="btn btn-secondary" aria-label="Resetează formularul">
                                <i class="fas fa-redo mr-1" aria-hidden="true"></i> Resetează
                            </button>
                        </div>
                    </form>
                </div>
            </div>

            <div class="card info-card mt-4">
                <div class="card-header bg-secondary text-white">
                    <h3 class="mb-0 d-flex align-items-center"><i class="fas fa-info-circle mr-2"></i>Informații despre Portal</h3>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-lg-8 col-md-12 mb-4 mb-lg-0">
                            <p class="lead">Portalul Dosare Judecătorești vă oferă acces la informații publice despre dosarele judecătorești din România, utilizând datele oficiale furnizate de Portalul Instanțelor de Judecată.</p>

                            <div class="row mt-4">
                                <div class="col-md-4 mb-4">
                                    <div class="card h-100 border-primary">
                                        <div class="card-body text-center">
                                            <div class="info-icon">
                                                <i class="fas fa-search"></i>
                                            </div>
                                            <h5 class="card-title">Căutare simplă</h5>
                                            <p class="card-text">Introduceți numărul dosarului sau numele unei părți pentru a găsi rapid informațiile de care aveți nevoie.</p>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-4 mb-4">
                                    <div class="card h-100 border-success">
                                        <div class="card-body text-center">
                                            <div class="info-icon">
                                                <i class="fas fa-filter"></i>
                                            </div>
                                            <h5 class="card-title">Filtrare avansată</h5>
                                            <p class="card-text">Utilizați filtrele avansate pentru a rafina căutarea după instanță sau interval de date.</p>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-4 mb-4">
                                    <div class="card h-100 border-info">
                                        <div class="card-body text-center">
                                            <div class="info-icon">
                                                <i class="fas fa-file-alt"></i>
                                            </div>
                                            <h5 class="card-title">Detalii complete</h5>
                                            <p class="card-text">Vizualizați informații detaliate despre dosare, părți implicate și termene de judecată.</p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-lg-4 col-md-12">
                            <div class="card bg-light h-100">
                                <div class="card-header">
                                    <h5 class="mb-0 d-flex align-items-center"><i class="fas fa-question-circle mr-2"></i>Cum să utilizați portalul</h5>
                                </div>
                                <div class="card-body">
                                    <ul class="list-group list-group-flush">
                                        <li class="list-group-item d-flex align-items-center">
                                            <i class="fas fa-check-circle text-success mr-3" style="font-size: 1.25rem;"></i>
                                            <span>Introduceți numărul dosarului pentru a găsi informații specifice</span>
                                        </li>
                                        <li class="list-group-item d-flex align-items-center">
                                            <i class="fas fa-check-circle text-success mr-3" style="font-size: 1.25rem;"></i>
                                            <span>Căutați după numele unei părți implicate în proces</span>
                                        </li>
                                        <li class="list-group-item d-flex align-items-center">
                                            <i class="fas fa-check-circle text-success mr-3" style="font-size: 1.25rem;"></i>
                                            <span>Filtrați rezultatele după instanță sau interval de date</span>
                                        </li>
                                        <li class="list-group-item d-flex align-items-center">
                                            <i class="fas fa-check-circle text-success mr-3" style="font-size: 1.25rem;"></i>
                                            <span>Vizualizați detalii complete despre dosare și termene</span>
                                        </li>
                                    </ul>
                                </div>
                            </div>

                            <div class="alert alert-warning mt-3 d-flex align-items-center">
                                <i class="fas fa-exclamation-triangle mr-3" style="font-size: 1.25rem;"></i>
                                <div>
                                    <strong>Notă:</strong> Toate informațiile afișate sunt preluate direct de la Portalul Instanțelor de Judecată și sunt actualizate în timp real.
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
</div><!-- /.content-wrapper -->

<script src="https://cdn.jsdelivr.net/npm/flatpickr@4.6.13/dist/flatpickr.min.js"></script>
<script src="https://cdn.jsdelivr.net/npm/flatpickr@4.6.13/dist/l10n/ro.js"></script>

<!-- Script specific pentru pagina principală -->
<script>
// Funcție pentru logging
function logDebug(message, data) {
    if (window.console && console.debug) {
        if (data) {
            console.debug('[DEBUG] ' + message, data);
        } else {
            console.debug('[DEBUG] ' + message);
        }
    }
}

document.addEventListener('DOMContentLoaded', function() {
    logDebug('DOM încărcat, inițializare funcționalități...');

    // Inițializare datepicker pentru câmpurile de dată
    initDatepickers();

    // Inițializare toggle pentru filtrele avansate
    initAdvancedFiltersToggle();

    // Inițializare funcționalitate de filtrare instanțe
    initInstantaFiltering();

    // Verificăm dacă există parametrul numeParte în URL și deschidem filtrele avansate
    const urlParams = new URLSearchParams(window.location.search);
    const numeParte = urlParams.get('numeParte');
    if (numeParte) {
        logDebug('Parametrul numeParte detectat în URL:', numeParte);

        // Deschidem filtrele avansate
        const advancedFilters = document.getElementById('advancedFilters');
        const advancedFiltersToggle = document.getElementById('advancedFiltersToggle');

        if (advancedFilters && advancedFiltersToggle) {
            advancedFilters.classList.add('show');

            // Actualizăm textul și iconița butonului
            const toggleText = advancedFiltersToggle.querySelector('span');
            const toggleIcon = advancedFiltersToggle.querySelector('.fa-chevron-down');

            if (toggleText) toggleText.textContent = 'Ascunde filtrele avansate';
            if (toggleIcon) toggleIcon.classList.replace('fa-chevron-down', 'fa-chevron-up');

            // Focusăm câmpul numeParte
            const numeParteInput = document.getElementById('numeParte');
            if (numeParteInput) {
                setTimeout(() => {
                    numeParteInput.focus();
                    // Selectăm textul pentru a permite utilizatorului să-l modifice ușor
                    numeParteInput.select();
                }, 100);
            }

            // Adăugăm un tooltip pentru a informa utilizatorul
            const numeParteFormGroup = document.querySelector('#numeParte').closest('.form-group');
            if (numeParteFormGroup) {
                const tooltip = document.createElement('div');
                tooltip.className = 'search-tooltip';
                tooltip.innerHTML = '<i class="fas fa-info-circle mr-1"></i> Câmpul a fost completat automat din pagina de detalii dosar. Puteți modifica sau completa criteriile de căutare.';

                numeParteFormGroup.appendChild(tooltip);

                // Ascundem tooltip-ul după 5 secunde
                setTimeout(() => {
                    tooltip.style.opacity = '0';
                    setTimeout(() => {
                        tooltip.remove();
                    }, 500);
                }, 5000);
            }

            // Verificăm dacă trebuie să trimitem automat formularul
            const autoSubmit = urlParams.get('autoSubmit');
            if (autoSubmit === 'true') {
                logDebug('Parametrul autoSubmit detectat, se trimite automat formularul');

                // Adăugăm un indicator de încărcare
                const loadingIndicator = document.getElementById('loading');
                if (loadingIndicator) {
                    loadingIndicator.style.display = 'block';
                }

                // Trimitem formularul după o scurtă întârziere pentru a permite utilizatorului să vadă ce se întâmplă
                setTimeout(() => {
                    const searchForm = document.getElementById('searchForm');
                    if (searchForm) {
                        searchForm.submit();
                    }
                }, 500);
            }
        }
    }

    // Validare formular de căutare și inițializare indicator de încărcare
    initSearchFormValidation();

    // Adaugă un handler pentru click-uri în afara calendarelor pentru a le închide
    document.addEventListener('click', function(e) {
        const target = e.target;
        const isDatepicker = target.classList.contains('datepicker') ||
                            target.closest('.flatpickr-calendar') ||
                            target.closest('.input-group-prepend');

        if (!isDatepicker) {
            const datepickers = document.querySelectorAll('.datepicker');
            datepickers.forEach(function(datepickerElement) {
                if (datepickerElement._flatpickr) {
                    datepickerElement._flatpickr.close();
                }
            });
        }
    });

    // Adaugă un handler pentru evenimentul de resize pentru a repoziția calendarele
    window.addEventListener('resize', function() {
        const datepickers = document.querySelectorAll('.datepicker');
        datepickers.forEach(function(datepickerElement) {
            if (datepickerElement._flatpickr && datepickerElement._flatpickr.isOpen) {
                datepickerElement._flatpickr.close();
                datepickerElement._flatpickr.open();
            }
        });
    });

    // Adaugă un handler pentru evenimentul de scroll pentru a închide calendarele
    window.addEventListener('scroll', function() {
        const datepickers = document.querySelectorAll('.datepicker');
        datepickers.forEach(function(datepickerElement) {
            if (datepickerElement._flatpickr && datepickerElement._flatpickr.isOpen) {
                datepickerElement._flatpickr.close();
            }
        });
    });
});

/**
 * Verifică și curăță valorile câmpurilor de dată care sunt invalide
 */
function cleanupCalendars() {
    // Curăță valorile câmpurilor de dată care sunt invalide
    const datepickers = document.querySelectorAll('.datepicker');
    datepickers.forEach(input => {
        if (input.value && !isValidDateFormat(input.value)) {
            input.value = '';
        }
    });
}

/**
 * Inițializează datepicker pentru câmpurile de dată
 */
function initDatepickers() {
    // Verifică dacă există elemente cu clasa 'datepicker'
    const datepickers = document.querySelectorAll('.datepicker');

    // Elimină orice instanță existentă de flatpickr
    datepickers.forEach(function(datepickerElement) {
        if (datepickerElement._flatpickr) {
            datepickerElement._flatpickr.destroy();
        }
    });

    if (datepickers.length > 0 && typeof flatpickr !== 'undefined') {
        // Configurație simplificată pentru flatpickr
        const flatpickrConfig = {
            dateFormat: 'd.m.Y',
            locale: 'ro',
            allowInput: true,
            disableMobile: true,
            monthSelectorType: 'dropdown',
            position: 'auto',
            static: false,
            clickOpens: true,
            time_24hr: true,
            enableTime: false
        };

        // Inițializare pentru toate elementele datepicker
        flatpickr('.datepicker', flatpickrConfig);

        // Adaugă event listeners pentru iconițele de calendar
        datepickers.forEach(function(datepickerElement) {
            const calendarIcon = datepickerElement.parentNode.querySelector('.input-group-prepend');
            if (calendarIcon) {
                calendarIcon.addEventListener('click', function(e) {
                    e.preventDefault();
                    e.stopPropagation();

                    if (datepickerElement._flatpickr) {
                        if (datepickerElement._flatpickr.isOpen) {
                            datepickerElement._flatpickr.close();
                        } else {
                            datepickerElement._flatpickr.open();
                        }
                    }
                });
            }
        });
    }
}

/**
 * Verifică dacă un string este în format de dată valid (ZZ.LL.AAAA)
 */
function isValidDateFormat(dateStr) {
    if (!dateStr) return false;

    // Verifică formatul ZZ.LL.AAAA
    const regex = /^\d{2}\.\d{2}\.\d{4}$/;
    if (!regex.test(dateStr)) return false;

    // Verifică dacă data este validă
    const parts = dateStr.split('.');
    const day = parseInt(parts[0], 10);
    const month = parseInt(parts[1], 10) - 1; // Lunile în JavaScript sunt indexate de la 0
    const year = parseInt(parts[2], 10);

    const date = new Date(year, month, day);

    return date.getFullYear() === year &&
           date.getMonth() === month &&
           date.getDate() === day;
}



/**
 * Inițializează validarea formularului de căutare și gestionează afișarea indicatorului de încărcare
 */
function initSearchFormValidation() {
    const searchForm = document.getElementById('searchForm');
    const loadingIndicator = document.getElementById('loading');

    if (!searchForm) {
        console.error('Formularul de căutare nu a fost găsit!');
        return;
    }

    if (!loadingIndicator) {
        console.error('Indicatorul de încărcare nu a fost găsit!');
        return;
    }

    // Verifică dacă formularul are deja un event listener atașat
    if (searchForm.hasAttribute('data-form-initialized')) {
        logDebug('Formularul este deja inițializat.');
        return;
    }

    // Marchează formularul ca fiind inițializat
    searchForm.setAttribute('data-form-initialized', 'true');

    searchForm.addEventListener('submit', function(e) {
        // Resetăm orice mesaje de eroare anterioare
        const existingAlerts = searchForm.querySelectorAll('.alert');
        existingAlerts.forEach(alert => alert.remove());

        // Resetăm starea câmpurilor
        const allInputs = searchForm.querySelectorAll('input');
        allInputs.forEach(input => input.classList.remove('is-invalid'));

        // Verifică dacă cel puțin un câmp de căutare este completat
        const numarDosar = document.getElementById('numarDosar').value.trim();
        const numeParte = document.getElementById('numeParte').value.trim();
        const obiectDosar = document.getElementById('obiectDosar').value.trim();

        // Verifică și câmpurile din filtrele avansate
        const institutie = document.getElementById('institutie').value.trim();
        const categorieCaz = document.getElementById('categorieCaz').value.trim();
        const dataStart = document.getElementById('dataStart').value.trim();
        const dataStop = document.getElementById('dataStop').value.trim();
        const dataUltimaModificareStart = document.getElementById('dataUltimaModificareStart').value.trim();
        const dataUltimaModificareStop = document.getElementById('dataUltimaModificareStop').value.trim();

        // Curăță datele de calendar înainte de validare
        cleanupCalendars();

        // Formularul este valid dacă cel puțin un câmp este completat
        const isValid = numarDosar !== '' ||
                       numeParte !== '' ||
                       obiectDosar !== '' ||
                       institutie !== '' ||
                       categorieCaz !== '' ||
                       dataStart !== '' ||
                       dataStop !== '' ||
                       dataUltimaModificareStart !== '' ||
                       dataUltimaModificareStop !== '';

        if (!isValid) {
            e.preventDefault();

            // Afișează mesaj de eroare personalizat cu animație
            const alertDiv = document.createElement('div');
            alertDiv.className = 'alert alert-danger mt-3';
            alertDiv.style.opacity = '0';
            alertDiv.style.transform = 'translateY(-10px)';
            alertDiv.style.transition = 'all 0.3s ease';
            alertDiv.innerHTML = '<i class="fas fa-exclamation-circle mr-2"></i><strong>Eroare:</strong> Vă rugăm să completați cel puțin un criteriu de căutare: număr dosar, nume parte, obiect dosar sau oricare dintre filtrele avansate.';

            // Adaugă mesajul de eroare la începutul formularului
            const formBody = searchForm.querySelector('.card-body');
            formBody.insertBefore(alertDiv, formBody.firstChild);

            // Trigger reflow pentru a aplica animația
            alertDiv.offsetHeight;

            // Animează mesajul de eroare
            alertDiv.style.opacity = '1';
            alertDiv.style.transform = 'translateY(0)';

            // Elimină mesajul de eroare după 5 secunde cu animație
            setTimeout(function() {
                alertDiv.style.opacity = '0';
                alertDiv.style.transform = 'translateY(-10px)';

                setTimeout(function() {
                    alertDiv.remove();
                }, 300);
            }, 5000);

            // Evidențiază câmpurile de căutare cu animație
            const fieldsToHighlight = [
                document.getElementById('numarDosar'),
                document.getElementById('numeParte'),
                document.getElementById('obiectDosar'),
                document.getElementById('institutie'),
                document.getElementById('dataStart'),
                document.getElementById('dataStop'),
                document.getElementById('dataUltimaModificareStart'),
                document.getElementById('dataUltimaModificareStop')
            ];

            fieldsToHighlight.forEach(function(field, index) {
                if (field) {
                    // Adăugăm un delay pentru fiecare câmp pentru un efect secvențial
                    setTimeout(() => {
                        field.classList.add('is-invalid');
                        // Adăugăm un efect de shake
                        field.style.animation = 'shake 0.5s';
                        setTimeout(() => {
                            field.style.animation = '';
                        }, 500);
                    }, index * 100);
                }
            });

            return false;
        }

        // Dacă formularul este valid, afișăm indicatorul de încărcare
        logDebug('Formular valid, se afișează indicatorul de încărcare...');

        // Afișează indicatorul de încărcare cu animație
        loadingIndicator.style.display = 'block';
        // Forțăm un reflow pentru a asigura aplicarea animației
        loadingIndicator.offsetHeight;
        loadingIndicator.classList.add('show');

        // Dezactivează butonul de trimitere pentru a preveni trimiteri multiple
        const submitButton = searchForm.querySelector('button[type="submit"]');
        if (submitButton) {
            // Salvăm conținutul original al butonului pentru a-l putea restaura dacă e nevoie
            if (!submitButton.hasAttribute('data-original-html')) {
                submitButton.setAttribute('data-original-html', submitButton.innerHTML);
            }

            // Adăugăm un wrapper pentru text pentru a putea aplica stiluri
            submitButton.innerHTML = '<i class="fas fa-spinner fa-spin mr-2" aria-hidden="true"></i><span>Se procesează...</span>';
            submitButton.disabled = true;
            submitButton.setAttribute('aria-busy', 'true');

            // Adăugăm o clasă pentru a aplica stiluri specifice
            submitButton.classList.add('btn-loading');

            // Actualizăm aria-label pentru a informa utilizatorii de tehnologii asistive
            submitButton.setAttribute('aria-label', 'Se procesează căutarea...');
        }

        // Adăugăm un delay mic pentru a permite utilizatorului să vadă animația
        // Acest delay nu afectează trimiterea formularului
        setTimeout(() => {
            // Formularul va fi trimis automat
            logDebug('Trimitere formular...');
        }, 100);

        // Adăugăm un feedback vizual pentru utilizator
        // Dacă filtrele avansate sunt vizibile, le evidențiem
        const advancedFilters = document.getElementById('advancedFilters');
        if (advancedFilters && advancedFilters.classList.contains('show')) {
            advancedFilters.style.backgroundColor = 'rgba(52, 152, 219, 0.05)';
            setTimeout(() => {
                advancedFilters.style.backgroundColor = '';
            }, 500);
        }
    });

    // Elimină clasa is-invalid când utilizatorul începe să tasteze
    const inputs = searchForm.querySelectorAll('input');
    inputs.forEach(function(input) {
        input.addEventListener('input', function() {
            this.classList.remove('is-invalid');
        });
    });

    // Adăugăm un stil pentru animația de shake
    const styleElement = document.createElement('style');
    styleElement.textContent = `
        @keyframes shake {
            0%, 100% { transform: translateX(0); }
            10%, 30%, 50%, 70%, 90% { transform: translateX(-5px); }
            20%, 40%, 60%, 80% { transform: translateX(5px); }
        }
    `;
    document.head.appendChild(styleElement);

    logDebug('Validare formular inițializată cu succes.');
}

/**
 * Inițializează toggle pentru filtrele avansate
 * Implementare îmbunătățită pentru a rezolva problemele de funcționalitate
 */
function initAdvancedFiltersToggle() {
    logDebug('Inițializare toggle pentru filtrele avansate...');

    // Obține elementele necesare
    const toggleBtn = document.getElementById('advancedFiltersToggle');
    const filtersSection = document.getElementById('advancedFilters');

    // Verifică dacă elementele există
    if (!toggleBtn || !filtersSection) {
        console.error('Elementele necesare pentru filtrele avansate nu au fost găsite!');
        return;
    }

    // Verifică dacă butonul are deja un event listener atașat
    if (toggleBtn.hasAttribute('data-initialized')) {
        logDebug('Butonul de filtre avansate este deja inițializat.');
        return;
    }

    // Marchează butonul ca fiind inițializat
    toggleBtn.setAttribute('data-initialized', 'true');

    // Adaugă event listener pentru click
    toggleBtn.addEventListener('click', function(e) {
        e.preventDefault();

        // Obține elementele din interiorul butonului
        const textSpan = this.querySelector('span');
        const icon = this.querySelector('.fa-chevron-down') || this.querySelector('.fa-chevron-up');

        // Toggle vizibilitate filtre avansate
        if (!filtersSection.classList.contains('show')) {
            // Afișează filtrele
            filtersSection.classList.add('show');

            // Actualizează textul și iconița
            if (textSpan) textSpan.textContent = 'Ascunde filtrele avansate';
            if (icon) {
                icon.classList.remove('fa-chevron-down');
                icon.classList.add('fa-chevron-up');
            }
        } else {
            // Ascunde filtrele
            filtersSection.classList.remove('show');

            // Actualizează textul și iconița
            if (textSpan) textSpan.textContent = 'Arată filtrele avansate';
            if (icon) {
                icon.classList.remove('fa-chevron-up');
                icon.classList.add('fa-chevron-down');
            }
        }
    });

    logDebug('Butonul de filtre avansate a fost inițializat cu succes.');
}

/**
 * Inițializează funcționalitatea de filtrare a instanțelor după categorie
 */
function initInstantaFiltering() {
    logDebug('Inițializare filtrare instanțe...');

    const categorieSelect = document.getElementById('categorieInstanta');
    const instantaSelect = document.getElementById('institutie');

    if (!categorieSelect || !instantaSelect) {
        logDebug('Elementele pentru filtrarea instanțelor nu au fost găsite.');
        return;
    }

    // Salvăm toate opțiunile originale
    const allOptions = Array.from(instantaSelect.options).slice(1); // Excludem prima opțiune "-- Selectați instanța --"

    categorieSelect.addEventListener('change', function() {
        const selectedCategory = this.value;

        // Resetăm selectorul de instanțe
        instantaSelect.innerHTML = '<option value="">-- Selectați instanța --</option>';

        // Filtrăm opțiunile în funcție de categoria selectată
        let filteredOptions = allOptions;

        if (selectedCategory) {
            filteredOptions = allOptions.filter(option => {
                const value = option.value;
                const text = option.textContent;

                switch (selectedCategory) {
                    case 'curtea_suprema':
                        return value.includes('InaltaCurte') || text.includes('Înalta Curte');
                    case 'curte_apel':
                        return value.includes('CurteadeApel') || text.includes('Curtea de Apel');
                    case 'tribunal':
                        return value.includes('Tribunalul') || text.includes('Tribunalul');
                    case 'judecatorie':
                        return value.includes('Judecatoria') || text.includes('Judecătoria');
                    default:
                        return true;
                }
            });
        }

        // Adăugăm opțiunile filtrate
        filteredOptions.forEach(option => {
            const newOption = option.cloneNode(true);
            instantaSelect.appendChild(newOption);
        });

        // Dacă avem o valoare selectată anterior, o restaurăm
        const selectedInstitutie = '<?php echo htmlspecialchars($_GET['institutie'] ?? ''); ?>';
        if (selectedInstitutie) {
            instantaSelect.value = selectedInstitutie;
        }

        logDebug('Filtrare instanțe completă. Opțiuni afișate:', filteredOptions.length);
    });

    // Inițializăm filtrul dacă avem o categorie selectată în URL
    const urlParams = new URLSearchParams(window.location.search);
    const selectedCategory = urlParams.get('categorieInstanta');
    if (selectedCategory) {
        categorieSelect.value = selectedCategory;
        categorieSelect.dispatchEvent(new Event('change'));
    }

    logDebug('Filtrarea instanțelor a fost inițializată cu succes.');
}

/**
 * Inițializează funcționalitatea de căutare rapidă prin click pe valorile din tabel
 */
function initQuickSearch() {
    logDebug('Inițializare căutare rapidă...');

    // Această funcție va fi implementată în search.php pentru rezultate
    // Aici o definim ca placeholder pentru consistență

    logDebug('Căutarea rapidă a fost inițializată.');
}

// Funcția initLoadingIndicator a fost integrată în initSearchFormValidation
</script>

<?php require_once 'includes/footer.php'; ?>
