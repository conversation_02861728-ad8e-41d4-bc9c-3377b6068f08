<?php
/**
 * Test script pentru verificarea encoding-ului UTF-8 în export CSV
 */

// Setăm encoding-ul pentru PHP
if (function_exists('mb_internal_encoding')) {
    mb_internal_encoding('UTF-8');
}

// Test data cu caractere românești
$testData = [
    ['Număr dosar', 'Instanță', 'Obiect', 'Data înregistrării', 'Nume parte relevantă'],
    ['123/2024', 'Judecătoria București', 'Contract de vânzare-cumpărare', '15.03.2024', 'SC EXEMPLU SRL'],
    ['456/2024', 'Tribunalul Iași', 'Acțiune în pretenții', '20.03.2024', '<PERSON>'],
    ['789/2024', '<PERSON>urtea de Apel Cluj', 'Recurs în anulare', '25.03.2024', '<PERSON>']
];

// Generăm fișierul CSV de test
header('Content-Type: text/csv; charset=UTF-8');
header('Content-Disposition: attachment; filename="test_utf8.csv"');
header('Content-Transfer-Encoding: 8bit');
header('Cache-Control: no-cache, no-store, must-revalidate');

// Curățăm toate buffer-ele
while (ob_get_level()) {
    ob_end_clean();
}

// Creăm output stream
$output = fopen('php://output', 'w');

// Scriem BOM UTF-8
fwrite($output, chr(0xEF) . chr(0xBB) . chr(0xBF));

// Scriem datele
foreach ($testData as $row) {
    fputcsv($output, $row, ';', '"');
}

fclose($output);
?>
