<?php
/**
 * Comprehensive test script for search functionality debugging
 */

// Include necessary files
require_once 'includes/config.php';
require_once 'includes/functions.php';
require_once 'src/Services/DosarService.php';

echo "<h1>Comprehensive Search Functionality Debug</h1>\n";
echo "<style>
    .debug-section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
    .success { background-color: #d4edda; border-color: #c3e6cb; }
    .error { background-color: #f8d7da; border-color: #f5c6cb; }
    .warning { background-color: #fff3cd; border-color: #ffeaa7; }
    .info { background-color: #d1ecf1; border-color: #bee5eb; }
    pre { background: #f8f9fa; padding: 10px; border-radius: 3px; overflow-x: auto; }
</style>\n";

// Test the specific problematic URL parameters
$testParams = [
    'institutie' => 'CurteadeApelBUCURESTI',
    'categorieInstanta' => 'curte_apel',
    'categorieCaz' => 'munca',
    'dataStart' => '01.01.2023',
    'dataStop' => '15.06.2025',
    'sortBy' => 'data',
    'sortDirection' => 'asc',
    '_maxResults' => 500,
    'page' => 1
];

echo "<div class='debug-section info'>";
echo "<h2>🔍 Testing Specific Problematic Parameters</h2>\n";
echo "<pre>" . print_r($testParams, true) . "</pre>\n";
echo "</div>";

try {
    // Test 0: Check SOAP configuration
    echo "<div class='debug-section'>";
    echo "<h2>Test 0: SOAP Configuration Check</h2>\n";
    echo "SOAP_WSDL: " . SOAP_WSDL . "<br>\n";
    echo "SOAP extension loaded: " . (extension_loaded('soap') ? '✅ Yes' : '❌ No') . "<br>\n";

    // Test SOAP WSDL accessibility
    $context = stream_context_create([
        'http' => [
            'timeout' => 10,
            'user_agent' => 'Judicial Portal Test Client/1.0'
        ]
    ]);

    $wsdlContent = @file_get_contents(SOAP_WSDL, false, $context);
    if ($wsdlContent !== false) {
        echo "✅ WSDL accessible (size: " . strlen($wsdlContent) . " bytes)<br>\n";
    } else {
        echo "❌ WSDL not accessible<br>\n";
    }
    echo "</div>";

    // Test 1: Initialize DosarService
    echo "<div class='debug-section'>";
    echo "<h2>Test 1: DosarService Initialization</h2>\n";
    $dosarService = new \App\Services\DosarService();
    echo "✅ DosarService initialized successfully<br>\n";
    echo "</div>";

    // Test 2: Parameter Processing and Validation
    echo "<div class='debug-section'>";
    echo "<h2>Test 2: Parameter Processing</h2>\n";

    // Process parameters exactly like search.php does
    $processedParams = [
        'numarDosar' => $testParams['numarDosar'] ?? '',
        'numeParte' => $testParams['numeParte'] ?? '',
        'obiectDosar' => $testParams['obiectDosar'] ?? '',
        'institutie' => $testParams['institutie'] ?? '',
        'categorieInstanta' => $testParams['categorieInstanta'] ?? '',
        'categorieCaz' => $testParams['categorieCaz'] ?? '',
        'dataStart' => $testParams['dataStart'] ?? '',
        'dataStop' => $testParams['dataStop'] ?? '',
        'dataUltimaModificareStart' => $testParams['dataUltimaModificareStart'] ?? '',
        'dataUltimaModificareStop' => $testParams['dataUltimaModificareStop'] ?? '',
        '_maxResults' => $testParams['_maxResults'] ?? 500
    ];

    // Special handling for institution parameter like in search.php
    if ($processedParams['institutie'] === '') {
        $processedParams['institutie'] = null;
    }

    echo "Processed parameters:<br>\n";
    echo "<pre>" . print_r($processedParams, true) . "</pre>\n";

    // Check if we have meaningful search criteria
    $hasSearchCriteria = !empty($processedParams['numarDosar']) || !empty($processedParams['numeParte']) ||
                        !empty($processedParams['obiectDosar']) || !empty($processedParams['institutie']) ||
                        !empty($processedParams['categorieInstanta']) || !empty($processedParams['categorieCaz']) ||
                        !empty($processedParams['dataStart']) || !empty($processedParams['dataStop']) ||
                        !empty($processedParams['dataUltimaModificareStart']) || !empty($processedParams['dataUltimaModificareStop']);

    echo "Has search criteria: " . ($hasSearchCriteria ? '✅ Yes' : '❌ No') . "<br>\n";
    echo "</div>";

    // Test 3: Institution Code Validation
    echo "<div class='debug-section'>";
    echo "<h2>Test 3: Institution Code Validation</h2>\n";

    $institutii = getInstanteList();
    $institutionCode = $testParams['institutie'];

    echo "Testing institution code: <strong>{$institutionCode}</strong><br>\n";
    echo "Institution exists in list: " . (isset($institutii[$institutionCode]) ? '✅ Yes' : '❌ No') . "<br>\n";

    if (isset($institutii[$institutionCode])) {
        echo "Institution name: <strong>" . htmlspecialchars($institutii[$institutionCode]) . "</strong><br>\n";
    } else {
        echo "❌ Institution code not found in list<br>\n";
        echo "Available institution codes (first 10):<br>\n";
        $codes = array_keys($institutii);
        echo "<pre>" . print_r(array_slice($codes, 0, 10), true) . "</pre>\n";
    }
    echo "</div>";

    // Test 4: Date Format Validation
    echo "<div class='debug-section'>";
    echo "<h2>Test 4: Date Format Processing</h2>\n";

    $dateStart = $testParams['dataStart'];
    $dateStop = $testParams['dataStop'];

    echo "Original date start: <strong>{$dateStart}</strong><br>\n";
    echo "Original date stop: <strong>{$dateStop}</strong><br>\n";

    // Test date parsing like DosarService does
    $startTimestamp = strtotime($dateStart);
    $stopTimestamp = strtotime($dateStop);

    echo "Start timestamp: " . ($startTimestamp !== false ? '✅ ' . date('Y-m-d', $startTimestamp) : '❌ Invalid') . "<br>\n";
    echo "Stop timestamp: " . ($stopTimestamp !== false ? '✅ ' . date('Y-m-d', $stopTimestamp) : '❌ Invalid') . "<br>\n";

    if ($startTimestamp && $stopTimestamp) {
        echo "Date range valid: " . ($startTimestamp <= $stopTimestamp ? '✅ Yes' : '❌ Start after stop') . "<br>\n";
    }
    echo "</div>";

    // Test 5: Perform actual search
    echo "<div class='debug-section'>";
    echo "<h2>Test 5: Perform Search with Test Parameters</h2>\n";

    $searchStart = microtime(true);
    $results = $dosarService->cautareAvansata($processedParams);
    $searchDuration = microtime(true) - $searchStart;

    echo "Search completed in " . round($searchDuration, 3) . " seconds<br>\n";
    echo "Results count: <strong>" . count($results) . "</strong><br>\n";

    if (!empty($results)) {
        echo "<div class='success'>";
        echo "✅ Search returned results<br>\n";
        echo "<h4>First result sample:</h4>\n";
        $firstResult = $results[0];
        echo "<pre>";
        echo "Număr: " . ($firstResult->numar ?? 'N/A') . "\n";
        echo "Instanță: " . ($firstResult->institutie ?? 'N/A') . "\n";
        echo "Obiect: " . ($firstResult->obiect ?? 'N/A') . "\n";
        echo "Data: " . ($firstResult->data ?? 'N/A') . "\n";
        echo "Stadiu: " . ($firstResult->stadiuProcesual ?? 'N/A') . "\n";
        echo "Părți: " . (isset($firstResult->parti) ? count($firstResult->parti) : 0) . " părți\n";
        echo "</pre>";
        echo "</div>";
    } else {
        echo "<div class='warning'>";
        echo "⚠️ Search returned no results<br>\n";
        echo "</div>";
    }
    echo "</div>";

    // Test 6: Check logs and SOAP communication
    echo "<div class='debug-section'>";
    echo "<h2>Test 6: Log Analysis</h2>\n";

    $logDir = 'logs';
    if (is_dir($logDir)) {
        $logFiles = glob($logDir . '/*.log');
        if (!empty($logFiles)) {
            echo "Log files found:<br>\n";
            foreach ($logFiles as $logFile) {
                echo "<h4>" . basename($logFile) . "</h4>\n";
                if (file_exists($logFile)) {
                    $logContent = file_get_contents($logFile);
                    $lines = explode("\n", $logContent);
                    $lastLines = array_slice($lines, -10); // Last 10 lines
                    echo "<pre>" . htmlspecialchars(implode("\n", $lastLines)) . "</pre>\n";
                }
            }
        } else {
            echo "No log files found in logs directory<br>\n";
        }
    } else {
        echo "Logs directory does not exist<br>\n";
        // Try to create it
        if (mkdir($logDir, 0755, true)) {
            echo "✅ Created logs directory<br>\n";
        } else {
            echo "❌ Failed to create logs directory<br>\n";
        }
    }
    echo "</div>";

    // Test 7: Simple search test
    echo "<div class='debug-section'>";
    echo "<h2>Test 7: Simple Search Test</h2>\n";

    $simpleParams = [
        'numarDosar' => '',
        'numeParte' => 'popescu',
        'obiectDosar' => '',
        'institutie' => null,
        'categorieInstanta' => '',
        'categorieCaz' => '',
        'dataStart' => '',
        'dataStop' => '',
        'dataUltimaModificareStart' => '',
        'dataUltimaModificareStop' => '',
        '_maxResults' => 10
    ];

    echo "Testing simple search with 'popescu':<br>\n";
    $simpleResults = $dosarService->cautareAvansata($simpleParams);
    echo "Simple search results: <strong>" . count($simpleResults) . "</strong><br>\n";

    if (!empty($simpleResults)) {
        echo "✅ Simple search works<br>\n";
    } else {
        echo "⚠️ Even simple search returns no results<br>\n";
    }
    echo "</div>";

    // Test 8: SOAP method inspection
    echo "<div class='debug-section'>";
    echo "<h2>Test 8: SOAP Service Inspection</h2>\n";

    try {
        $soapClient = new SoapClient(SOAP_WSDL, [
            'soap_version' => SOAP_1_2,
            'exceptions' => true,
            'trace' => true,
            'cache_wsdl' => WSDL_CACHE_NONE
        ]);

        $functions = $soapClient->__getFunctions();
        echo "Available SOAP functions:<br>\n";
        echo "<pre>" . htmlspecialchars(implode("\n", array_slice($functions, 0, 5))) . "</pre>\n";

        $types = $soapClient->__getTypes();
        echo "Available SOAP types (first 3):<br>\n";
        echo "<pre>" . htmlspecialchars(implode("\n", array_slice($types, 0, 3))) . "</pre>\n";

    } catch (Exception $e) {
        echo "❌ SOAP inspection failed: " . $e->getMessage() . "<br>\n";
    }
    echo "</div>";

} catch (Exception $e) {
    echo "<div class='debug-section error'>";
    echo "<h2>❌ Critical Error</h2>\n";
    echo "<strong>Error:</strong> " . htmlspecialchars($e->getMessage()) . "<br>\n";
    echo "<strong>File:</strong> " . $e->getFile() . "<br>\n";
    echo "<strong>Line:</strong> " . $e->getLine() . "<br>\n";
    echo "<h4>Stack trace:</h4>\n";
    echo "<pre>" . htmlspecialchars($e->getTraceAsString()) . "</pre>\n";
    echo "</div>";
}

echo "<div class='debug-section info'>";
echo "<h2>🏁 Debug Test Complete</h2>\n";
echo "<p>This comprehensive test should help identify the exact issue with the search functionality.</p>";
echo "</div>";
?>
