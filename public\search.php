<?php

/**
 * Portal Judiciar - Pagina de căutare
 */

// Încărcăm bootstrap-ul aplicației
require_once dirname(__DIR__) . '/bootstrap.php';

// Importăm clasele necesare
use App\Helpers\TemplateEngine;
use App\Services\DosarService;

// Inițializăm motorul de șabloane
$templateEngine = new TemplateEngine();

// Lista instanțelor
$instante = [
    'JudecatoriaSECTORUL1BUCURESTI' => 'Judecătoria Sectorului 1 București',
    'JudecatoriaSECTORUL2BUCURESTI' => 'Judecătoria Sectorului 2 București',
    'JudecatoriaSECTORUL3BUCURESTI' => 'Judecătoria Sectorului 3 București',
    'JudecatoriaSECTORUL4BUCURESTI' => 'Judecătoria Sectorului 4 București',
    'JudecatoriaSECTORUL5BUCURESTI' => 'Judecătoria Sectorului 5 București',
    'JudecatoriaSECTORUL6BUCURESTI' => 'Judecătoria Sectorului 6 București',
    'TribunalulBUCURESTI' => 'Tribunalul București',
    'CurteadeApelBUCURESTI' => 'Curtea de Apel București',
    'JudecatoriaCLUJNAPOCA' => 'Judecătoria Cluj-Napoca',
    'TribunalulCLUJ' => 'Tribunalul Cluj',
    'CurteadeApelCLUJ' => 'Curtea de Apel Cluj',
    'JudecatoriaCONSTANTA' => 'Judecătoria Constanța',
    'TribunalulCONSTANTA' => 'Tribunalul Constanța',
    'CurteadeApelCONSTANTA' => 'Curtea de Apel Constanța',
    'JudecatoriaTIMISOARA' => 'Judecătoria Timișoara',
    'TribunalulTIMIS' => 'Tribunalul Timiș',
    'CurteadeApelTIMISOARA' => 'Curtea de Apel Timișoara',
    'JudecatoriaIASI' => 'Judecătoria Iași',
    'TribunalulIASI' => 'Tribunalul Iași',
    'CurteadeApelIASI' => 'Curtea de Apel Iași',
    'JudecatoriaBRASOV' => 'Judecătoria Brașov',
    'TribunalulBRASOV' => 'Tribunalul Brașov',
    'CurteadeApelBRASOV' => 'Curtea de Apel Brașov',
    'InaltaCurtedeCasatiesiJustitie' => 'Înalta Curte de Casație și Justiție'
];

// Parametrii de căutare
$searchParams = [
    'numarDosar' => $_GET['numarDosar'] ?? '',
    'institutie' => $_GET['institutie'] ?? '',
    'numeParte' => $_GET['numeParte'] ?? '',
    'obiectDosar' => $_GET['obiectDosar'] ?? '',
    'dataStart' => $_GET['dataStart'] ?? '',
    'dataStop' => $_GET['dataStop'] ?? '',
    'dataUltimaModificareStart' => $_GET['dataUltimaModificareStart'] ?? '',
    'dataUltimaModificareStop' => $_GET['dataUltimaModificareStop'] ?? '',
    '_maxResults' => 500 // Limităm numărul de rezultate pentru performanță optimă
];

// Verificăm dacă avem criterii de căutare
$hasSearchCriteria = !empty($searchParams['numarDosar']) || 
                     !empty($searchParams['numeParte']) || 
                     !empty($searchParams['obiectDosar']) || 
                     !empty($searchParams['dataStart']) || 
                     !empty($searchParams['dataStop']) || 
                     !empty($searchParams['dataUltimaModificareStart']) || 
                     !empty($searchParams['dataUltimaModificareStop']);

// Rezultatele căutării
$results = [];

/**
 * Determină criteriile de potrivire pentru un dosar specific
 * @param object $dosar Obiectul dosar
 * @param array $searchParams Parametrii de căutare folosiți
 * @return string Textul cu criteriile de potrivire
 */
function getMatchingCriteria($dosar, $searchParams) {
    $criteria = [];

    // Verifică potrivirea pentru numărul dosarului
    if (!empty($searchParams['numarDosar'])) {
        if (stripos($dosar->numar, $searchParams['numarDosar']) !== false) {
            $criteria[] = "Număr: " . htmlspecialchars($searchParams['numarDosar']);
        }
    }

    // Verifică potrivirea pentru numele părții
    if (!empty($searchParams['numeParte'])) {
        $searchTerm = $searchParams['numeParte'];
        $found = false;

        // Căutăm în obiectul dosarului
        if (stripos($dosar->obiect, $searchTerm) !== false) {
            $found = true;
        }

        // Căutăm în părțile implicate dacă există
        if (isset($dosar->parti) && is_array($dosar->parti)) {
            foreach ($dosar->parti as $parte) {
                if (isset($parte->nume) && stripos($parte->nume, $searchTerm) !== false) {
                    $found = true;
                    break;
                }
            }
        }

        if ($found) {
            $criteria[] = "Parte: " . htmlspecialchars($searchTerm);
        }
    }

    // Verifică potrivirea pentru obiectul dosarului
    if (!empty($searchParams['obiectDosar'])) {
        if (stripos($dosar->obiect, $searchParams['obiectDosar']) !== false) {
            $criteria[] = "Obiect: " . htmlspecialchars($searchParams['obiectDosar']);
        }
    }

    // Verifică potrivirea pentru instanță
    if (!empty($searchParams['institutie'])) {
        if ($dosar->institutie === $searchParams['institutie']) {
            global $instante;
            $numeInstanta = $instante[$searchParams['institutie']] ?? $searchParams['institutie'];
            $criteria[] = "Instanță: " . htmlspecialchars($numeInstanta);
        }
    }

    // Verifică potrivirea pentru intervalul de date
    if (!empty($searchParams['dataStart']) || !empty($searchParams['dataStop'])) {
        $dataDosar = $dosar->data;
        $inInterval = true;

        if (!empty($searchParams['dataStart'])) {
            $dataStart = DateTime::createFromFormat('d.m.Y', $searchParams['dataStart']);
            $dataDosar_obj = DateTime::createFromFormat('d.m.Y', $dataDosar);
            if ($dataStart && $dataDosar_obj && $dataDosar_obj < $dataStart) {
                $inInterval = false;
            }
        }

        if (!empty($searchParams['dataStop']) && $inInterval) {
            $dataStop = DateTime::createFromFormat('d.m.Y', $searchParams['dataStop']);
            $dataDosar_obj = DateTime::createFromFormat('d.m.Y', $dataDosar);
            if ($dataStop && $dataDosar_obj && $dataDosar_obj > $dataStop) {
                $inInterval = false;
            }
        }

        if ($inInterval) {
            $intervalText = '';
            if (!empty($searchParams['dataStart']) && !empty($searchParams['dataStop'])) {
                $intervalText = "Interval: " . htmlspecialchars($searchParams['dataStart']) . " - " . htmlspecialchars($searchParams['dataStop']);
            } elseif (!empty($searchParams['dataStart'])) {
                $intervalText = "De la: " . htmlspecialchars($searchParams['dataStart']);
            } elseif (!empty($searchParams['dataStop'])) {
                $intervalText = "Până la: " . htmlspecialchars($searchParams['dataStop']);
            }
            if ($intervalText) {
                $criteria[] = $intervalText;
            }
        }
    }

    // Dacă nu s-a găsit niciun criteriu specific, returnăm un text generic
    if (empty($criteria)) {
        return "Căutare generală";
    }

    return implode("; ", $criteria);
}

// Dacă avem criterii de căutare, efectuăm căutarea
if ($hasSearchCriteria) {
    try {
        // Inițializăm serviciul de dosare
        $dosarService = new DosarService();

        // Efectuăm căutarea avansată
        $results = $dosarService->cautareAvansata($searchParams);



        // Adăugăm un mesaj flash dacă nu avem rezultate
        if (empty($results)) {
            TemplateEngine::addFlashMessage('info', 'Nu au fost găsite rezultate pentru criteriile de căutare specificate.');
        } else {
            TemplateEngine::addFlashMessage('success', 'Au fost găsite ' . count($results) . ' rezultate.');
        }
    } catch (Exception $e) {
        // Adăugăm un mesaj flash cu eroarea
        TemplateEngine::addFlashMessage('danger', 'Eroare la căutare: ' . $e->getMessage());

        // Logăm eroarea
        error_log('Eroare la căutare: ' . $e->getMessage());
    }
} else {
    // Adăugăm un mesaj flash dacă nu avem criterii de căutare
    TemplateEngine::addFlashMessage('warning', 'Vă rugăm să specificați cel puțin un criteriu de căutare (număr dosar, nume parte, obiect dosar sau interval de date).');
}

// Datele pentru șablon
$data = [
    'instante' => $instante,
    'searchParams' => $searchParams,
    'results' => $results,
    'searchQuery' => http_build_query($searchParams)
];

// Afișăm șablonul
echo $templateEngine->render('search.twig', $data);
