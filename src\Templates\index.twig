{% extends "layouts/main.twig" %}

{% block title %}{{ app.name }} - Căutare dosare{% endblock %}

{% block content %}
    <div class="row">
        <div class="col-md-12">
            <div class="card mb-4">
                <div class="card-header bg-primary text-white">
                    <h2 class="card-title h5 mb-0">
                        <i class="fas fa-search me-2"></i>Căutare rapidă
                    </h2>
                </div>
                <div class="card-body">
                    <form action="search.php" method="get" class="row g-3">
                        <div class="col-md-6">
                            <label for="numarDosar" class="form-label">Număr dosar:</label>
                            <input type="text" class="form-control" id="numarDosar" name="numarDosar" placeholder="Ex: 1234/123/2023" value="{{ get('numarDosar') }}">
                        </div>
                        <div class="col-md-6">
                            <label for="institutie" class="form-label">Instanță:</label>
                            <select class="form-select" id="institutie" name="institutie">
                                <option value="">Toate instanțele</option>
                                {% for code, name in instante %}
                                    <option value="{{ code }}" {% if get('institutie') == code %}selected{% endif %}>{{ name }}</option>
                                {% endfor %}
                            </select>
                        </div>
                        <div class="col-12 text-end">
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-search me-1"></i>Caută
                            </button>
                        </div>
                    </form>
                </div>
            </div>

            <div class="card" id="cautare-avansata">
                <div class="card-header bg-primary text-white">
                    <h2 class="card-title h5 mb-0">
                        <i class="fas fa-search-plus me-2"></i>Căutare avansată
                    </h2>
                </div>
                <div class="card-body">
                    <form action="search.php" method="get" class="row g-3">
                        <div class="col-md-6">
                            <label for="numarDosar" class="form-label">Număr dosar:</label>
                            <input type="text" class="form-control" id="numarDosar" name="numarDosar" placeholder="Ex: 1234/123/2023" value="{{ get('numarDosar') }}">
                        </div>
                        <div class="col-md-6">
                            <label for="institutie" class="form-label">Instanță:</label>
                            <select class="form-select" id="institutie" name="institutie">
                                <option value="">Toate instanțele</option>
                                {% for code, name in instante %}
                                    <option value="{{ code }}" {% if get('institutie') == code %}selected{% endif %}>{{ name }}</option>
                                {% endfor %}
                            </select>
                        </div>
                        <div class="col-md-6">
                            <label for="numeParte" class="form-label">Nume parte:</label>
                            <input type="text" class="form-control" id="numeParte" name="numeParte" placeholder="Ex: Popescu Ion" value="{{ get('numeParte') }}">
                        </div>
                        <div class="col-md-6">
                            <label for="obiectDosar" class="form-label">Obiect dosar:</label>
                            <input type="text" class="form-control" id="obiectDosar" name="obiectDosar" placeholder="Ex: pretenții" value="{{ get('obiectDosar') }}">
                        </div>
                        <div class="col-md-6">
                            <label for="dataStart" class="form-label">Data început:</label>
                            <input type="date" class="form-control" id="dataStart" name="dataStart" value="{{ get('dataStart') }}">
                        </div>
                        <div class="col-md-6">
                            <label for="dataStop" class="form-label">Data sfârșit:</label>
                            <input type="date" class="form-control" id="dataStop" name="dataStop" value="{{ get('dataStop') }}">
                        </div>
                        <div class="col-12 text-end">
                            <button type="reset" class="btn btn-secondary">
                                <i class="fas fa-undo me-1"></i>Resetează
                            </button>
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-search me-1"></i>Caută
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
{% endblock %}

{% block javascripts %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Adăugăm funcționalitate pentru butonul de resetare
        document.querySelector('button[type="reset"]').addEventListener('click', function(e) {
            e.preventDefault();
            const form = this.closest('form');
            const inputs = form.querySelectorAll('input, select');
            inputs.forEach(input => {
                if (input.type === 'text' || input.type === 'date' || input.tagName === 'SELECT') {
                    input.value = '';
                }
            });
        });
    });
</script>
{% endblock %}
