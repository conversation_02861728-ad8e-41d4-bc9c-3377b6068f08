<?php
/**
 * Test script for institution code validation
 */

require_once 'includes/config.php';
require_once 'includes/functions.php';

echo "<h1>Institution Code Validation Test</h1>\n";
echo "<style>
    .test-section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
    .success { background-color: #d4edda; border-color: #c3e6cb; }
    .error { background-color: #f8d7da; border-color: #f5c6cb; }
    .warning { background-color: #fff3cd; border-color: #ffeaa7; }
    pre { background: #f8f9fa; padding: 10px; border-radius: 3px; overflow-x: auto; }
</style>\n";

// Test the specific institution code from the problematic URL
$testInstitutionCode = 'CurteadeApelBUCURESTI';

echo "<div class='test-section'>";
echo "<h2>Testing Institution Code: <strong>{$testInstitutionCode}</strong></h2>\n";

try {
    // Get the list of institutions
    $institutii = getInstanteList();
    
    echo "<p>Total institutions in list: <strong>" . count($institutii) . "</strong></p>\n";
    
    // Check if the specific code exists
    if (isset($institutii[$testInstitutionCode])) {
        echo "<div class='success'>";
        echo "<p>✅ Institution code found!</p>\n";
        echo "<p><strong>Full name:</strong> " . htmlspecialchars($institutii[$testInstitutionCode]) . "</p>\n";
        echo "</div>";
    } else {
        echo "<div class='error'>";
        echo "<p>❌ Institution code NOT found in the list!</p>\n";
        echo "</div>";
        
        // Search for similar codes
        echo "<div class='warning'>";
        echo "<h3>Searching for similar codes...</h3>\n";
        
        $similarCodes = [];
        foreach ($institutii as $code => $name) {
            if (stripos($code, 'Bucuresti') !== false || 
                stripos($code, 'BUCURESTI') !== false ||
                stripos($name, 'Bucuresti') !== false ||
                stripos($name, 'BUCURESTI') !== false) {
                $similarCodes[$code] = $name;
            }
        }
        
        if (!empty($similarCodes)) {
            echo "<p>Found " . count($similarCodes) . " similar codes:</p>\n";
            echo "<pre>";
            foreach ($similarCodes as $code => $name) {
                echo htmlspecialchars($code) . " => " . htmlspecialchars($name) . "\n";
            }
            echo "</pre>";
        } else {
            echo "<p>No similar codes found.</p>\n";
        }
        echo "</div>";
        
        // Show first 20 institution codes for reference
        echo "<div class='test-section'>";
        echo "<h3>First 20 institution codes for reference:</h3>\n";
        echo "<pre>";
        $codes = array_keys($institutii);
        for ($i = 0; $i < min(20, count($codes)); $i++) {
            echo htmlspecialchars($codes[$i]) . " => " . htmlspecialchars($institutii[$codes[$i]]) . "\n";
        }
        echo "</pre>";
        echo "</div>";
    }
    
} catch (Exception $e) {
    echo "<div class='error'>";
    echo "<p>❌ Error getting institution list: " . htmlspecialchars($e->getMessage()) . "</p>\n";
    echo "</div>";
}

echo "</div>";

// Test with a simple search to see if the issue is with the institution code
echo "<div class='test-section'>";
echo "<h2>Test Simple Search Without Institution Filter</h2>\n";

try {
    require_once 'src/Services/DosarService.php';
    
    $dosarService = new \App\Services\DosarService();
    
    $simpleParams = [
        'numarDosar' => '',
        'numeParte' => '',
        'obiectDosar' => '',
        'institutie' => null, // No institution filter
        'dataStart' => '01.01.2023',
        'dataStop' => '15.06.2025',
        'dataUltimaModificareStart' => '',
        'dataUltimaModificareStop' => '',
        '_maxResults' => 10
    ];
    
    echo "<p>Testing search with date range only (no institution filter)...</p>\n";
    $results = $dosarService->cautareAvansata($simpleParams);
    
    echo "<p>Results: <strong>" . count($results) . "</strong></p>\n";
    
    if (!empty($results)) {
        echo "<div class='success'>";
        echo "<p>✅ Search without institution filter works!</p>\n";
        echo "<p>This suggests the issue might be with the institution code.</p>\n";
        echo "</div>";
    } else {
        echo "<div class='warning'>";
        echo "<p>⚠️ Even simple search returns no results.</p>\n";
        echo "<p>This suggests a broader issue with the SOAP API or date formatting.</p>\n";
        echo "</div>";
    }
    
} catch (Exception $e) {
    echo "<div class='error'>";
    echo "<p>❌ Error during simple search: " . htmlspecialchars($e->getMessage()) . "</p>\n";
    echo "</div>";
}

echo "</div>";

echo "<h2>Test Complete</h2>\n";
?>
