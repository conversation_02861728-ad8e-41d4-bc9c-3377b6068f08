/**
 * Modern Footer Styles for Judicial Portal
 * Designed for contemporary, smart, and ergonomic user experience
 */

/* Global Layout Fixes for Footer */
html, body {
    height: 100%;
    margin: 0;
    padding: 0;
}

body {
    display: flex;
    flex-direction: column;
    min-height: 100vh;
}

.content-wrapper {
    flex: 1 0 auto;
}

/* CSS Variables for Footer Design System */
:root {
    /* Footer Color Palette - Consistent with Site Theme */
    --footer-primary: #2c3e50;
    --footer-secondary: #007bff;
    --footer-accent: #007bff;
    --footer-light: #e2e8f0;
    --footer-text: #ffffff;
    --footer-text-muted: #cbd5e0;
    --footer-border: #495057;
    --footer-hover: #0056b3;
    
    /* Spacing and Layout */
    --footer-padding-y: 2rem;
    --footer-padding-x: 1.5rem;
    --footer-section-gap: 1.5rem;
    --footer-item-gap: 0.5rem;
    
    /* Typography */
    --footer-font-size-base: 0.95rem;
    --footer-font-size-small: 0.875rem;
    --footer-font-size-heading: 1.125rem;
    --footer-line-height: 1.6;
    
    /* Interactive Elements */
    --footer-touch-target: 44px;
    --footer-border-radius: 6px;
    --footer-transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    
    /* Shadows and Effects */
    --footer-shadow: 0 -4px 20px rgba(0, 0, 0, 0.1);
    --footer-glow: 0 0 20px rgba(65, 153, 225, 0.3);
}

/* Main Footer Container */
.modern-footer {
    background: var(--footer-primary);
    color: var(--footer-text);
    padding: 1rem 0;
    margin-top: auto;
    border-top: 3px solid var(--footer-secondary);
    width: 100%;
    clear: both;
}

/* Compact Footer Links */
.footer-links {
    display: flex;
    flex-wrap: wrap;
    gap: 1.5rem;
    justify-content: flex-end;
}

.footer-link {
    color: var(--footer-text-muted);
    text-decoration: none;
    font-size: 0.9rem;
    transition: color 0.3s ease;
    padding: 0.25rem 0;
}

.footer-link:hover,
.footer-link:focus {
    color: var(--footer-text);
    text-decoration: none;
}

.modern-footer p {
    color: var(--footer-text-muted);
    font-size: 0.9rem;
    margin: 0;
}

/* Back to Top Button */
.back-to-top {
    position: fixed;
    bottom: 2rem;
    right: 2rem;
    width: 3.5rem;
    height: 3.5rem;
    background: linear-gradient(135deg, var(--footer-accent), var(--footer-hover));
    color: white;
    border: none;
    border-radius: 50%;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.25rem;
    opacity: 0;
    visibility: hidden;
    transform: translateY(20px);
    transition: var(--footer-transition);
    z-index: 1000;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.2);
    -webkit-backdrop-filter: blur(10px);
    backdrop-filter: blur(10px);
}

.back-to-top.visible {
    opacity: 1;
    visibility: visible;
    transform: translateY(0);
}

.back-to-top:hover {
    transform: translateY(-3px);
    box-shadow: var(--footer-glow), 0 8px 30px rgba(0, 0, 0, 0.3);
}

.back-to-top:active {
    transform: translateY(-1px);
}

/* Responsive Design */
@media (max-width: 767.98px) {
    .modern-footer {
        padding: 0.75rem 0;
        text-align: center;
    }

    .footer-links {
        justify-content: center;
        gap: 1rem;
        margin-top: 0.5rem;
    }

    .back-to-top {
        bottom: 1.5rem;
        right: 1.5rem;
        width: 3rem;
        height: 3rem;
        font-size: 1rem;
    }
}

@media (max-width: 575.98px) {
    .footer-links {
        gap: 0.75rem;
    }

    .footer-link {
        font-size: 0.85rem;
    }

    .modern-footer p {
        font-size: 0.85rem;
    }
}

/* High contrast mode support */
@media (prefers-contrast: high) {
    .modern-footer {
        background: #000;
        border-top: 2px solid #fff;
    }

    .footer-link:hover {
        background: #fff;
        color: #000;
    }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
    .footer-link,
    .back-to-top {
        transition: none;
    }

    .back-to-top:hover {
        transform: none;
    }
}

/* Print styles */
@media print {
    .modern-footer {
        background: none !important;
        color: #000 !important;
        border-top: 1px solid #000;
    }

    .footer-link,
    .modern-footer p {
        color: #000 !important;
    }

    .back-to-top {
        display: none !important;
    }
}
