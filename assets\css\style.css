/* Stiluri generale */
body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    line-height: 1.6;
    color: #333;
    background-color: #f8f9fa;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 15px;
}



/* Stiluri pentru formulare */
.search-form {
    background-color: #fff;
    padding: 1.5rem;
    border-radius: 5px;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
    margin-bottom: 2rem;
}

.form-group {
    margin-bottom: 1rem;
}

.form-group label {
    display: block;
    margin-bottom: 0.5rem;
    font-weight: 600;
}

.form-control {
    width: 100%;
    padding: 0.5rem;
    border: 1px solid #ced4da;
    border-radius: 4px;
    font-size: 1rem;
}

.form-row {
    display: flex;
    margin: 0 -10px;
}

.form-col {
    flex: 1;
    padding: 0 10px;
}

.btn {
    display: inline-block;
    font-weight: 400;
    text-align: center;
    white-space: nowrap;
    vertical-align: middle;
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
    border: 1px solid transparent;
    padding: 0.5rem 1rem;
    font-size: 1rem;
    line-height: 1.5;
    border-radius: 0.25rem;
    cursor: pointer;
}

.btn-primary {
    color: #fff;
    background-color: #007bff;
    border-color: #007bff;
}

.btn-primary:hover {
    background-color: #0069d9;
    border-color: #0062cc;
}

/* Stiluri pentru rezultate */
.results {
    background-color: #fff;
    border-radius: 5px;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
    margin-bottom: 2rem;
}

.results-header {
    padding: 1rem;
    border-bottom: 1px solid #e9ecef;
}

.results-header h2 {
    margin: 0;
    font-size: 1.5rem;
}

.results-body {
    padding: 1rem;
}

.result-item {
    padding: 1rem;
    border-bottom: 1px solid #e9ecef;
}

.result-item:last-child {
    border-bottom: none;
}

.result-item h3 {
    margin: 0 0 0.5rem;
    font-size: 1.2rem;
}

.result-item p {
    margin: 0 0 0.5rem;
}

.result-item .meta {
    font-size: 0.9rem;
    color: #6c757d;
}

/* Stiluri pentru paginare */
.pagination {
    display: flex;
    justify-content: center;
    margin-top: 2rem;
}

.page-link {
    padding: 0.5rem 0.75rem;
    margin: 0 0.25rem;
    border: 1px solid #dee2e6;
    border-radius: 0.25rem;
    color: #007bff;
    text-decoration: none;
}

.page-link:hover {
    background-color: #e9ecef;
}

.page-link.active {
    background-color: #007bff;
    color: #fff;
    border-color: #007bff;
}

.page-link.disabled {
    color: #6c757d;
    pointer-events: none;
    cursor: default;
}

/* Stiluri pentru detalii dosar */
.dosar-details {
    background-color: #fff;
    padding: 1.5rem;
    border-radius: 5px;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
    margin-bottom: 2rem;
}

.dosar-header {
    margin-bottom: 1.5rem;
    padding-bottom: 1rem;
    border-bottom: 1px solid #e9ecef;
}

.dosar-header h2 {
    margin: 0 0 0.5rem;
}

.dosar-section {
    margin-bottom: 1.5rem;
}

.dosar-section h3 {
    margin: 0 0 1rem;
    font-size: 1.2rem;
    padding-bottom: 0.5rem;
    border-bottom: 1px solid #e9ecef;
}

.dosar-section table {
    width: 100%;
    border-collapse: collapse;
}

.dosar-section table th,
.dosar-section table td {
    padding: 0.5rem;
    text-align: left;
    border-bottom: 1px solid #e9ecef;
}

.dosar-section table th {
    font-weight: 600;
}

/* Stiluri pentru footer */
footer {
    background-color: #343a40;
    color: #fff;
    padding: 1rem 0;
    margin-top: 2rem;
}

footer p {
    margin: 0;
}





/* Stiluri responsive */
@media (max-width: 768px) {
    .form-row {
        flex-direction: column;
    }

    .form-col {
        margin-bottom: 1rem;
    }

    .form-col:last-child {
        margin-bottom: 0;
    }

    .card-header h2 {
        font-size: 1.5rem;
    }

    .btn {
        width: 100%;
        margin-bottom: 0.5rem;
    }

    .btn-group .btn {
        width: auto;
        margin-bottom: 0;
    }

    .table-responsive {
        border: none;
    }

    .table th, .table td {
        font-size: 0.875rem;
        padding: 0.5rem;
    }

    .export-actions {
        flex-direction: column;
    }

    .export-actions .btn {
        margin-bottom: 0.5rem;
    }
}

@media (max-width: 576px) {
    .card-body {
        padding: 1rem;
    }



    .dosar-header h3 {
        font-size: 1.25rem;
    }

    .dosar-section h4 {
        font-size: 1.1rem;
    }
}
