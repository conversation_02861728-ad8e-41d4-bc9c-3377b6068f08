<?php
/**
 * Simple log viewer for debugging
 */

echo "<h1>Search Debug Logs</h1>\n";
echo "<style>
    .log-section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
    .log-content { background: #f8f9fa; padding: 10px; border-radius: 3px; overflow-x: auto; white-space: pre-wrap; font-family: monospace; max-height: 400px; overflow-y: auto; }
    .refresh-btn { background: #007bff; color: white; padding: 10px 20px; border: none; border-radius: 5px; cursor: pointer; margin: 10px 0; }
</style>\n";

echo "<button class='refresh-btn' onclick='location.reload()'>🔄 Refresh Logs</button>\n";

$logDir = 'logs';

if (is_dir($logDir)) {
    $logFiles = glob($logDir . '/*.log');
    
    if (!empty($logFiles)) {
        // Sort by modification time (newest first)
        usort($logFiles, function($a, $b) {
            return filemtime($b) - filemtime($a);
        });
        
        foreach ($logFiles as $logFile) {
            $fileName = basename($logFile);
            $fileSize = filesize($logFile);
            $lastModified = date('Y-m-d H:i:s', filemtime($logFile));
            
            echo "<div class='log-section'>";
            echo "<h2>📄 {$fileName}</h2>\n";
            echo "<p><strong>Size:</strong> {$fileSize} bytes | <strong>Last Modified:</strong> {$lastModified}</p>\n";
            
            if ($fileSize > 0) {
                $content = file_get_contents($logFile);
                $lines = explode("\n", $content);
                
                // Show last 50 lines
                $lastLines = array_slice($lines, -50);
                $displayContent = implode("\n", $lastLines);
                
                echo "<div class='log-content'>" . htmlspecialchars($displayContent) . "</div>\n";
                
                if (count($lines) > 50) {
                    echo "<p><em>Showing last 50 lines of " . count($lines) . " total lines.</em></p>\n";
                }
            } else {
                echo "<p><em>Log file is empty.</em></p>\n";
            }
            
            echo "</div>";
        }
    } else {
        echo "<div class='log-section'>";
        echo "<p>No log files found in the logs directory.</p>\n";
        echo "</div>";
    }
} else {
    echo "<div class='log-section'>";
    echo "<p>Logs directory does not exist.</p>\n";
    
    // Try to create it
    if (mkdir($logDir, 0755, true)) {
        echo "<p>✅ Created logs directory.</p>\n";
    } else {
        echo "<p>❌ Failed to create logs directory.</p>\n";
    }
    echo "</div>";
}

// Show PHP error log if accessible
$phpErrorLog = ini_get('error_log');
if ($phpErrorLog && file_exists($phpErrorLog)) {
    echo "<div class='log-section'>";
    echo "<h2>📄 PHP Error Log</h2>\n";
    echo "<p><strong>Path:</strong> {$phpErrorLog}</p>\n";
    
    $content = file_get_contents($phpErrorLog);
    $lines = explode("\n", $content);
    $lastLines = array_slice($lines, -20); // Last 20 lines
    $displayContent = implode("\n", $lastLines);
    
    echo "<div class='log-content'>" . htmlspecialchars($displayContent) . "</div>\n";
    echo "</div>";
}

echo "<h2>Debug Complete</h2>\n";
?>
