<?php
/**
 * Comprehensive debugging for combined advanced filter issues
 */

require_once 'includes/config.php';
require_once 'includes/functions.php';
require_once 'src/Services/DosarService.php';

echo "<h1>🔍 Combined Advanced Filters Debug</h1>\n";
echo "<style>
    .debug-section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
    .success { background-color: #d4edda; border-color: #c3e6cb; }
    .error { background-color: #f8d7da; border-color: #f5c6cb; }
    .warning { background-color: #fff3cd; border-color: #ffeaa7; }
    .info { background-color: #d1ecf1; border-color: #bee5eb; }
    pre { background: #f8f9fa; padding: 10px; border-radius: 3px; overflow-x: auto; max-height: 400px; overflow-y: auto; }
    .test-result { margin: 5px 0; padding: 8px; border-radius: 3px; }
    .pass { background-color: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
    .fail { background-color: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
    .skip { background-color: #fff3cd; color: #856404; border: 1px solid #ffeaa7; }
    .test-url { background: #f8f9fa; padding: 10px; border-radius: 3px; margin: 10px 0; border-left: 4px solid #007bff; }
    .filter-step { background: #e9ecef; padding: 8px; margin: 5px 0; border-radius: 3px; border-left: 3px solid #6c757d; }
</style>\n";

echo "<div class='debug-section info'>";
echo "<h2>🎯 Investigating Combined Filter Issue</h2>\n";
echo "<p><strong>Problem:</strong> Advanced search not returning expected results with combined filters</p>";
echo "<p><strong>Test Case:</strong> TribunalulBUCURESTI + tribunal category + contencios_administrativ + future dates</p>";
echo "</div>";

// Define the failing test parameters
$failingParams = [
    'numarDosar' => '',
    'numeParte' => '',
    'obiectDosar' => '',
    'institutie' => 'TribunalulBUCURESTI',
    'categorieInstanta' => 'tribunal',
    'categorieCaz' => 'contencios_administrativ',
    'dataStart' => '01.05.2025',
    'dataStop' => '15.06.2025',
    'dataUltimaModificareStart' => '',
    'dataUltimaModificareStop' => '',
    '_maxResults' => 100
];

try {
    $dosarService = new \App\Services\DosarService();
    
    // Test 1: Isolated Component Testing
    echo "<div class='debug-section'>";
    echo "<h2>Test 1: Isolated Component Testing</h2>\n";
    
    $isolatedTests = [
        [
            'name' => 'Institution Only (SOAP)',
            'params' => [
                'institutie' => 'TribunalulBUCURESTI',
                '_maxResults' => 20
            ],
            'description' => 'Test raw SOAP API with only institution parameter'
        ],
        [
            'name' => 'Institution Category Only (Client-side)',
            'params' => [
                'categorieInstanta' => 'tribunal',
                '_maxResults' => 20
            ],
            'description' => 'Test client-side tribunal category filtering'
        ],
        [
            'name' => 'Case Category Only (Client-side)',
            'params' => [
                'categorieCaz' => 'contencios_administrativ',
                '_maxResults' => 20
            ],
            'description' => 'Test client-side administrative litigation filtering'
        ],
        [
            'name' => 'Date Range Only',
            'params' => [
                'dataStart' => '01.05.2025',
                'dataStop' => '15.06.2025',
                '_maxResults' => 20
            ],
            'description' => 'Test future date range filtering alone'
        ]
    ];
    
    foreach ($isolatedTests as $test) {
        echo "<h4>1." . (array_search($test, $isolatedTests) + 1) . " {$test['name']}</h4>\n";
        echo "<p><em>{$test['description']}</em></p>\n";
        
        // Prepare full parameters with defaults
        $testParams = array_merge([
            'numarDosar' => '',
            'numeParte' => '',
            'obiectDosar' => '',
            'institutie' => null,
            'categorieInstanta' => '',
            'categorieCaz' => '',
            'dataStart' => '',
            'dataStop' => '',
            'dataUltimaModificareStart' => '',
            'dataUltimaModificareStop' => '',
        ], $test['params']);
        
        echo "<div class='test-url'>";
        echo "<strong>Test Parameters:</strong><br>";
        foreach ($testParams as $key => $value) {
            if (!empty($value) && $key !== '_maxResults') {
                echo "{$key}: {$value}<br>";
            }
        }
        echo "</div>";
        
        try {
            $searchStart = microtime(true);
            $results = $dosarService->cautareAvansata($testParams);
            $searchDuration = microtime(true) - $searchStart;
            
            echo "<div class='test-result " . (count($results) > 0 ? 'pass' : 'warning') . "'>";
            echo "Results: " . count($results) . " cases in " . round($searchDuration, 3) . "s";
            echo "</div>";
            
            if (!empty($results)) {
                // Analyze institution distribution
                $institutionDistribution = [];
                $categoryDistribution = [];
                $dateDistribution = [];
                
                foreach ($results as $result) {
                    // Institution analysis
                    $institution = $result->institutie ?? 'N/A';
                    $institutionDistribution[$institution] = ($institutionDistribution[$institution] ?? 0) + 1;
                    
                    // Category analysis
                    $category = $result->categorieCaz ?? 'N/A';
                    $categoryDistribution[$category] = ($categoryDistribution[$category] ?? 0) + 1;
                    
                    // Date analysis
                    $date = $result->data ?? '';
                    if (!empty($date)) {
                        $year = date('Y', strtotime($date));
                        $dateDistribution[$year] = ($dateDistribution[$year] ?? 0) + 1;
                    }
                }
                
                // Show distributions
                if (!empty($institutionDistribution)) {
                    echo "<div class='filter-step'>";
                    echo "<strong>Institution Distribution:</strong> ";
                    foreach (array_slice($institutionDistribution, 0, 3, true) as $inst => $count) {
                        echo "{$inst}: {$count}; ";
                    }
                    if (count($institutionDistribution) > 3) {
                        echo "... +" . (count($institutionDistribution) - 3) . " more";
                    }
                    echo "</div>";
                }
                
                if (!empty($categoryDistribution)) {
                    echo "<div class='filter-step'>";
                    echo "<strong>Category Distribution:</strong> ";
                    foreach (array_slice($categoryDistribution, 0, 3, true) as $cat => $count) {
                        echo "{$cat}: {$count}; ";
                    }
                    if (count($categoryDistribution) > 3) {
                        echo "... +" . (count($categoryDistribution) - 3) . " more";
                    }
                    echo "</div>";
                }
                
                if (!empty($dateDistribution)) {
                    echo "<div class='filter-step'>";
                    echo "<strong>Date Distribution:</strong> ";
                    ksort($dateDistribution);
                    foreach ($dateDistribution as $year => $count) {
                        echo "{$year}: {$count}; ";
                    }
                    echo "</div>";
                }
                
                // Show sample results
                echo "<h5>Sample results (first 2):</h5>";
                for ($i = 0; $i < min(2, count($results)); $i++) {
                    $result = $results[$i];
                    echo "<div class='test-result info'>";
                    echo "Case: " . htmlspecialchars($result->numar ?? 'N/A');
                    echo " | Date: " . htmlspecialchars($result->data ?? 'N/A');
                    echo " | Institution: " . htmlspecialchars($result->institutie ?? 'N/A');
                    echo " | Category: " . htmlspecialchars($result->categorieCaz ?? 'N/A');
                    echo "</div>";
                }
            }
            
        } catch (Exception $e) {
            echo "<div class='test-result fail'>";
            echo "❌ Test failed: " . htmlspecialchars($e->getMessage());
            echo "</div>";
        }
    }
    
    echo "</div>";
    
    // Test 2: Pattern Matching Verification
    echo "<div class='debug-section'>";
    echo "<h2>Test 2: Pattern Matching Verification</h2>\n";
    
    echo "<h4>2.1 Institution Category Pattern Matching</h4>\n";
    
    // Test tribunal pattern matching
    $testInstitutions = [
        'TribunalulBUCURESTI',
        'Tribunalul BUCURESTI',
        'Tribunalul Bucuresti',
        'TRIBUNALUL BUCURESTI',
        'TribunalulCONSTANTA',
        'CurteadeApelBUCURESTI',
        'JudecatoriaSECTORUL1BUCURESTI'
    ];
    
    foreach ($testInstitutions as $institution) {
        $institutieNormalized = strtolower(str_replace([' ', '-', '_'], '', $institution));
        
        // Test tribunal pattern matching logic
        $matchesTribunal = false;
        $patterns = ['tribunalul', 'tribunal '];
        foreach ($patterns as $pattern) {
            if (stripos($institutieNormalized, $pattern) !== false) {
                $matchesTribunal = true;
                break;
            }
        }
        
        $expected = (stripos($institution, 'tribunal') !== false && stripos($institution, 'curte') === false && stripos($institution, 'judecatoria') === false);
        $testPassed = ($matchesTribunal === $expected);
        
        echo "<div class='test-result " . ($testPassed ? 'pass' : 'fail') . "'>";
        echo "'{$institution}' → Tribunal: " . ($matchesTribunal ? 'YES' : 'NO');
        echo " " . ($testPassed ? '✅' : '❌');
        if (!$testPassed) {
            echo " (Expected: " . ($expected ? 'YES' : 'NO') . ")";
        }
        echo "</div>";
    }
    
    echo "<h4>2.2 Administrative Litigation Terminology Matching</h4>\n";
    
    // Test administrative litigation terminology
    $testCases = [
        'contencios administrativ',
        'CONTENCIOS ADMINISTRATIV',
        'litigiu administrativ',
        'drept administrativ',
        'administrative',
        'administrativ',
        'contencios',
        'civil',
        'penal',
        'comercial'
    ];
    
    foreach ($testCases as $testCase) {
        $testCaseLower = strtolower($testCase);
        
        // Test administrative litigation matching logic
        $matchesAdmin = false;
        $adminTerms = ['contencios administrativ', 'contencios', 'administrativ', 'administrative', 
                      'litigiu administrativ', 'drept administrativ', 'admin'];
        
        foreach ($adminTerms as $term) {
            if (stripos($testCaseLower, $term) !== false) {
                $matchesAdmin = true;
                break;
            }
        }
        
        $expected = (stripos($testCase, 'admin') !== false || stripos($testCase, 'contencios') !== false);
        $testPassed = ($matchesAdmin === $expected);
        
        echo "<div class='test-result " . ($testPassed ? 'pass' : 'fail') . "'>";
        echo "'{$testCase}' → Admin Litigation: " . ($matchesAdmin ? 'YES' : 'NO');
        echo " " . ($testPassed ? '✅' : '❌');
        if (!$testPassed) {
            echo " (Expected: " . ($expected ? 'YES' : 'NO') . ")";
        }
        echo "</div>";
    }
    
    echo "</div>";
    
    // Test 3: Full Combination Testing with Step-by-Step Analysis
    echo "<div class='debug-section'>";
    echo "<h2>Test 3: Full Combination Testing</h2>\n";

    echo "<h4>3.1 Progressive Filter Application</h4>\n";

    $progressiveTests = [
        [
            'name' => 'Institution + Date Range',
            'params' => [
                'institutie' => 'TribunalulBUCURESTI',
                'dataStart' => '01.05.2025',
                'dataStop' => '15.06.2025',
                '_maxResults' => 50
            ]
        ],
        [
            'name' => 'Institution + Institution Category',
            'params' => [
                'institutie' => 'TribunalulBUCURESTI',
                'categorieInstanta' => 'tribunal',
                '_maxResults' => 50
            ]
        ],
        [
            'name' => 'Institution + Case Category',
            'params' => [
                'institutie' => 'TribunalulBUCURESTI',
                'categorieCaz' => 'contencios_administrativ',
                '_maxResults' => 50
            ]
        ],
        [
            'name' => 'All Filters Combined',
            'params' => $failingParams
        ]
    ];

    foreach ($progressiveTests as $test) {
        echo "<h5>3." . (array_search($test, $progressiveTests) + 1) . " {$test['name']}</h5>\n";

        // Prepare full parameters
        $testParams = array_merge([
            'numarDosar' => '',
            'numeParte' => '',
            'obiectDosar' => '',
            'institutie' => null,
            'categorieInstanta' => '',
            'categorieCaz' => '',
            'dataStart' => '',
            'dataStop' => '',
            'dataUltimaModificareStart' => '',
            'dataUltimaModificareStop' => '',
        ], $test['params']);

        echo "<div class='test-url'>";
        echo "<strong>Parameters:</strong> ";
        $activeParams = [];
        foreach ($testParams as $key => $value) {
            if (!empty($value) && $key !== '_maxResults') {
                $activeParams[] = "{$key}={$value}";
            }
        }
        echo implode(', ', $activeParams);
        echo "</div>";

        try {
            $searchStart = microtime(true);
            $results = $dosarService->cautareAvansata($testParams);
            $searchDuration = microtime(true) - $searchStart;

            echo "<div class='test-result " . (count($results) > 0 ? 'pass' : 'warning') . "'>";
            echo "Results: " . count($results) . " cases in " . round($searchDuration, 3) . "s";
            echo "</div>";

            if (!empty($results)) {
                // Quick analysis
                $sample = $results[0];
                echo "<div class='filter-step'>";
                echo "Sample: " . htmlspecialchars($sample->numar ?? 'N/A');
                echo " | " . htmlspecialchars($sample->institutie ?? 'N/A');
                echo " | " . htmlspecialchars($sample->categorieCaz ?? 'N/A');
                echo " | " . htmlspecialchars($sample->data ?? 'N/A');
                echo "</div>";
            } else {
                echo "<div class='test-result warning'>";
                echo "⚠️ No results found - checking if this is expected or indicates a filtering issue";
                echo "</div>";
            }

        } catch (Exception $e) {
            echo "<div class='test-result fail'>";
            echo "❌ Test failed: " . htmlspecialchars($e->getMessage());
            echo "</div>";
        }
    }

    echo "</div>";

    // Test 4: Client-Side Filtering Analysis
    echo "<div class='debug-section'>";
    echo "<h2>Test 4: Client-Side Filtering Deep Analysis</h2>\n";

    echo "<h4>4.1 Manual Client-Side Filter Testing</h4>\n";

    // First get raw SOAP results without client-side filtering
    $rawSoapParams = [
        'numarDosar' => '',
        'numeParte' => '',
        'obiectDosar' => '',
        'institutie' => 'TribunalulBUCURESTI',
        'dataStart' => '01.05.2025',
        'dataStop' => '15.06.2025',
        'dataUltimaModificareStart' => '',
        'dataUltimaModificareStop' => '',
        '_maxResults' => 100
    ];

    echo "<div class='filter-step'>";
    echo "<strong>Step 1:</strong> Getting raw SOAP results (no client-side filtering)";
    echo "</div>";

    try {
        $rawResults = $dosarService->cautareAvansata($rawSoapParams);

        echo "<div class='test-result " . (count($rawResults) > 0 ? 'pass' : 'warning') . "'>";
        echo "Raw SOAP Results: " . count($rawResults) . " cases";
        echo "</div>";

        if (!empty($rawResults)) {
            echo "<div class='filter-step'>";
            echo "<strong>Step 2:</strong> Analyzing raw results for tribunal pattern matching";
            echo "</div>";

            $tribunalMatches = 0;
            $adminMatches = 0;
            $bothMatches = 0;

            foreach ($rawResults as $result) {
                $institution = $result->institutie ?? '';
                $category = $result->categorieCaz ?? '';
                $categoryName = $result->categorieCazNume ?? '';
                $object = $result->obiect ?? '';

                // Test tribunal matching
                $institutieNormalized = strtolower(str_replace([' ', '-', '_'], '', $institution));
                $matchesTribunal = false;
                $patterns = ['tribunalul', 'tribunal '];
                foreach ($patterns as $pattern) {
                    if (stripos($institutieNormalized, $pattern) !== false) {
                        $matchesTribunal = true;
                        break;
                    }
                }

                if ($matchesTribunal) {
                    $tribunalMatches++;
                }

                // Test administrative litigation matching
                $matchesAdmin = false;
                $adminTerms = ['contencios administrativ', 'contencios', 'administrativ', 'administrative',
                              'litigiu administrativ', 'drept administrativ', 'admin'];

                $searchFields = [$category, $categoryName, $object];
                foreach ($searchFields as $field) {
                    $fieldLower = strtolower($field);
                    foreach ($adminTerms as $term) {
                        if (stripos($fieldLower, $term) !== false) {
                            $matchesAdmin = true;
                            break 2;
                        }
                    }
                }

                if ($matchesAdmin) {
                    $adminMatches++;
                }

                if ($matchesTribunal && $matchesAdmin) {
                    $bothMatches++;
                }
            }

            echo "<div class='test-result info'>";
            echo "Tribunal pattern matches: {$tribunalMatches} / " . count($rawResults);
            echo "</div>";

            echo "<div class='test-result info'>";
            echo "Administrative litigation matches: {$adminMatches} / " . count($rawResults);
            echo "</div>";

            echo "<div class='test-result " . ($bothMatches > 0 ? 'pass' : 'warning') . "'>";
            echo "Both filters match: {$bothMatches} / " . count($rawResults);
            echo "</div>";

            if ($bothMatches > 0) {
                echo "<div class='test-result pass'>";
                echo "✅ Expected results exist in raw data - client-side filtering should work";
                echo "</div>";
            } else {
                echo "<div class='test-result warning'>";
                echo "⚠️ No cases match both criteria in raw data - this may be expected";
                echo "</div>";
            }

            // Show samples of each type
            echo "<h5>Sample Analysis:</h5>";
            $sampleCount = 0;
            foreach ($rawResults as $result) {
                if ($sampleCount >= 3) break;

                $institution = $result->institutie ?? '';
                $category = $result->categorieCaz ?? '';

                echo "<div class='filter-step'>";
                echo "Case: " . htmlspecialchars($result->numar ?? 'N/A');
                echo " | Institution: " . htmlspecialchars($institution);
                echo " | Category: " . htmlspecialchars($category);
                echo "</div>";

                $sampleCount++;
            }
        }

    } catch (Exception $e) {
        echo "<div class='test-result fail'>";
        echo "❌ Raw SOAP test failed: " . htmlspecialchars($e->getMessage());
        echo "</div>";
    }

    echo "</div>";

    echo "<div class='debug-section info'>";
    echo "<h2>🏁 Comprehensive Combined Filter Debug Complete</h2>\n";
    echo "<p><strong>Analysis completed:</strong></p>";
    echo "<ul>";
    echo "<li>✅ Isolated component testing</li>";
    echo "<li>✅ Pattern matching verification</li>";
    echo "<li>✅ Progressive filter combination testing</li>";
    echo "<li>✅ Client-side filtering deep analysis</li>";
    echo "</ul>";
    echo "<p><strong>Next step:</strong> Review results and implement targeted fixes.</p>";
    echo "</div>";

} catch (Exception $e) {
    echo "<div class='debug-section error'>";
    echo "<h2>❌ Critical Error</h2>\n";
    echo "<div class='test-result fail'>";
    echo "Error: " . htmlspecialchars($e->getMessage());
    echo "</div>";
    echo "</div>";
}
