/**
 * Script pentru funcționalitățile interactive ale aplicației
 * Versiune actualizată pentru a evita conflicte cu codul din index.php
 */

// Acest fișier este menținut pentru compatibilitate, dar funcționalitățile
// sunt implementate direct în paginile respective pentru a evita conflicte

// Inițializare la încărcarea documentului
document.addEventListener('DOMContentLoaded', function() {
    // Inițializare datepicker pentru câmpurile de dată - dacă funcția există
    if (typeof initDatepickers === 'function' &&
        document.querySelector('.datepicker') &&
        !document.querySelector('.datepicker')._flatpickr) {
        initDatepickers();
    }

    // Validare formular de căutare - dacă funcția există
    if (typeof initSearchFormValidation === 'function' &&
        document.getElementById('searchForm') &&
        !document.getElementById('searchForm').hasAttribute('data-validation-initialized')) {
        initSearchFormValidation();
    }

    // Nu inițializăm filtrele avansate de aici pentru a evita conflicte
    // Această funcționalitate este gestionată direct în index.php
});

// Definim funcțiile doar dacă nu există deja
if (typeof initDatepickers !== 'function') {
    /**
     * Inițializează datepicker pentru câmpurile de dată
     * Această funcție este definită doar dacă nu există deja
     */
    function initDatepickers() {
        console.log('Inițializare datepicker din script.js');
        // Verifică dacă există elemente cu clasa 'datepicker'
        const datepickers = document.querySelectorAll('.datepicker');

        if (datepickers.length > 0 && typeof flatpickr !== 'undefined') {
            flatpickr('.datepicker', {
                dateFormat: 'd.m.Y',
                locale: 'ro',
                allowInput: true,
                disableMobile: true,
                monthSelectorType: 'dropdown',
                position: 'auto'
            });
        }
    }
}

if (typeof initSearchFormValidation !== 'function') {
    /**
     * Inițializează validarea formularului de căutare
     * Această funcție este definită doar dacă nu există deja
     */
    function initSearchFormValidation() {
        console.log('Inițializare validare formular din script.js');
        const searchForm = document.getElementById('searchForm');

        if (searchForm) {
            // Marcăm formularul ca fiind inițializat
            searchForm.setAttribute('data-validation-initialized', 'true');

            searchForm.addEventListener('submit', function(e) {
                let isValid = false;

                // Verifică dacă cel puțin un câmp de căutare este completat
                const numarDosar = document.getElementById('numarDosar').value.trim();
                const numeParte = document.getElementById('numeParte').value.trim();
                const obiectDosar = document.getElementById('obiectDosar').value.trim();

                if (numarDosar !== '' || numeParte !== '' || obiectDosar !== '') {
                    isValid = true;
                }

                if (!isValid) {
                    e.preventDefault();

                    // Afișează mesaj de eroare personalizat
                    const alertDiv = document.createElement('div');
                    alertDiv.className = 'alert alert-danger mt-3';
                    alertDiv.innerHTML = '<i class="fas fa-exclamation-circle mr-2"></i><strong>Eroare:</strong> Vă rugăm să completați cel puțin un criteriu de căutare: număr dosar, nume parte sau obiect dosar.';

                    // Adaugă mesajul de eroare la începutul formularului
                    const formBody = searchForm.querySelector('.card-body');
                    if (formBody) {
                        formBody.insertBefore(alertDiv, formBody.firstChild);

                        // Elimină mesajul de eroare după 5 secunde
                        setTimeout(function() {
                            alertDiv.remove();
                        }, 5000);
                    }
                }
            });
        }
    }
}

// Nu redefinim funcția initAdvancedFiltersToggle pentru a evita conflicte
// Această funcționalitate este gestionată direct în index.php
