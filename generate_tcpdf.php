<?php
/**
 * Script pentru generarea PDF-urilor din detaliile dosarelor folosind TCPDF
 * Implementare cu pixel-perfect visual parity cu interfața web
 */

// Configurare pentru producție - dezactivăm afișarea erorilor
error_reporting(E_ALL);
ini_set('display_errors', 0);

// Definim constantele necesare dacă nu sunt deja definite
if (!defined('LOG_DIR')) {
    define('LOG_DIR', __DIR__ . '/logs');
}

if (!defined('CACHE_DIR')) {
    define('CACHE_DIR', __DIR__ . '/cache');
}

// Creăm directoarele dacă nu există
if (!is_dir(LOG_DIR)) {
    mkdir(LOG_DIR, 0755, true);
}

if (!is_dir(CACHE_DIR)) {
    mkdir(CACHE_DIR, 0755, true);
}

// Includere fișiere necesare
try {
    require_once 'includes/config.php';
    require_once 'includes/functions.php';
    require_once 'services/DosarService.php';

    // Configurare TCPDF
    require_once 'src/Config/tcpdf_config.php';
} catch (Exception $e) {
    die("Eroare la încărcarea fișierelor: " . $e->getMessage());
}

// Inițializare variabile
$error = null;
$pdfContent = null;

// Verificare parametri
$numarDosar = isset($_GET['numar']) ? trim($_GET['numar']) : '';
$institutie = isset($_GET['institutie']) ? trim($_GET['institutie']) : '';
$disposition = isset($_GET['disposition']) ? trim($_GET['disposition']) : 'attachment'; // 'attachment' pentru descărcare

// Verificare dacă parametrii necesari sunt prezenți
if (empty($numarDosar) || empty($institutie)) {
    $error = "Parametrii necesari pentru generarea PDF lipsesc.";
    $errorCode = "PDF-002";
} else {
    try {
        $dosar = null;
        $useMockData = false;

        // Încercăm să obținem datele reale din DosarService
        try {
            // Verificăm dacă extensia SOAP este disponibilă
            if (!extension_loaded('soap')) {
                throw new Exception("Extensia SOAP nu este disponibilă");
            }

            // Verificăm dacă constantele SOAP sunt definite
            if (!defined('SOAP_1_2')) {
                throw new Exception("Constantele SOAP nu sunt definite");
            }

            // Inițializare serviciu pentru căutare
            $dosarService = new DosarService();

            // Obținere detalii dosar
            $dosar = $dosarService->getDetaliiDosar($numarDosar, $institutie);

            // Verificăm dacă dosarul este valid și nu este gol
            if (!$dosar || empty((array)$dosar) || empty($dosar->numar)) {
                throw new Exception("Nu s-au găsit detalii pentru dosarul specificat");
            }

        } catch (Exception $e) {
            // Logăm eroarea pentru debugging
            $logMessage = date('Y-m-d H:i:s') . " - Eroare la obținerea datelor reale pentru dosarul {$numarDosar}: " . $e->getMessage() . "\n";
            file_put_contents(LOG_DIR . '/pdf_errors.log', $logMessage, FILE_APPEND);

            $useMockData = true;
        }

        // Dacă nu am putut obține date reale, folosim date mock
        if ($useMockData || !$dosar) {
            $dosar = (object)[
                'numar' => $numarDosar,
                'numarVechi' => '',
                'institutie' => $institutie,
                'departament' => 'Secția Civilă',
                'data' => '01.01.2024',
                'obiect' => 'Test obiect pentru dosarul ' . $numarDosar . ' (Date demonstrative)',
                'stadiuProcesualNume' => 'În curs de judecată',
                'categorieCazNume' => 'Civil',
                'dataModificare' => date('d.m.Y H:i'),
                'parti' => [
                    [
                        'nume' => 'Test Reclamant SRL',
                        'calitate' => 'Reclamant'
                    ],
                    [
                        'nume' => 'Test Pârât SA',
                        'calitate' => 'Pârât'
                    ]
                ],
                'sedinte' => [
                    [
                        'data' => '15.01.2024',
                        'dataPronuntare' => '15.01.2024',
                        'ora' => '10:00',
                        'complet' => 'Complet 1',
                        'solutie' => 'Se amână pentru data de 15.02.2024 pentru citarea părților și administrarea probelor solicitate de reclamant. Cheltuieli de judecată în sarcina celui care le-a pricinuit.',
                        'solutieSumar' => 'Amânare pentru citare părți',
                        'numarDocument' => '123',
                        'dataDocument' => '15.01.2024',
                        'documentSedinta' => 'Încheiere'
                    ],
                    [
                        'data' => '15.02.2024',
                        'dataPronuntare' => '',
                        'ora' => '11:30',
                        'complet' => 'Complet 1',
                        'solutie' => 'Se fixează termen pentru pronunțare pe data de 01.03.2024.',
                        'solutieSumar' => 'Termen pentru pronunțare',
                        'numarDocument' => '',
                        'dataDocument' => '',
                        'documentSedinta' => ''
                    ]
                ],
                'caiAtac' => []
            ];
        }

        // Verificăm dacă TCPDF este disponibil
        $tcpdfPath = __DIR__ . '/vendor/tecnickcom/tcpdf/tcpdf.php';
        if (!file_exists($tcpdfPath)) {
            // Încercăm calea alternativă
            $tcpdfPath = dirname(__DIR__) . '/vendor/tecnickcom/tcpdf/tcpdf.php';
            if (!file_exists($tcpdfPath)) {
                throw new Exception("TCPDF nu este instalat sau calea este incorectă. Căutat în: " . $tcpdfPath);
            }
        }

        // Includem TCPDF
        require_once $tcpdfPath;

        // Creăm directorul pentru loguri dacă nu există
        if (!is_dir(LOG_DIR)) {
            mkdir(LOG_DIR, 0755, true);
        }

        // Generăm PDF-ul
        $pdfContent = generatePDFWithTCPDF($dosar, $numarDosar, $institutie);

        // Logăm succesul
        $logMessage = date('Y-m-d H:i:s') . " - PDF generat cu succes pentru dosarul {$numarDosar}\n";
        file_put_contents(LOG_DIR . '/pdf_export.log', $logMessage, FILE_APPEND);

        // Trimitem PDF-ul către browser
        header('Content-Type: application/pdf');
        header('Content-Length: ' . strlen($pdfContent));
        
        // Setăm header-ul Content-Disposition pentru descărcare
        $filename = "dosar_{$numarDosar}_" . date('Y-m-d') . ".pdf";
        $filename = preg_replace('/[^a-zA-Z0-9._-]/', '_', $filename);
        header("Content-Disposition: {$disposition}; filename=\"{$filename}\"");
        
        // Cache control
        header('Cache-Control: private, max-age=0, must-revalidate');
        header('Pragma: public');

        echo $pdfContent;
        exit;

    } catch (Exception $e) {
        $error = "Eroare la generarea PDF: " . $e->getMessage();
        $errorCode = "PDF-003";
        
        // Logăm eroarea
        $logMessage = date('Y-m-d H:i:s') . " - Eroare la generarea PDF pentru dosarul {$numarDosar}: " . $e->getMessage() . "\n";
        file_put_contents(LOG_DIR . '/pdf_errors.log', $logMessage, FILE_APPEND);
    }
}

// Dacă avem o eroare, afișăm o pagină de eroare
if ($error) {
    http_response_code(500);
    ?>
    <!DOCTYPE html>
    <html lang="ro">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>Eroare la generarea PDF</title>
        <style>
            body { font-family: Arial, sans-serif; margin: 40px; background-color: #f8f9fa; }
            .error-container { background-color: white; padding: 30px; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); max-width: 600px; margin: 0 auto; }
            .error-title { color: #dc3545; font-size: 24px; margin-bottom: 20px; }
            .error-description { color: #6c757d; line-height: 1.6; margin-bottom: 20px; }
            .error-code { background-color: #f8f9fa; padding: 10px; border-radius: 4px; font-family: monospace; }
            .back-link { display: inline-block; margin-top: 20px; color: #007bff; text-decoration: none; }
            .back-link:hover { text-decoration: underline; }
        </style>
    </head>
    <body>
        <div class="error-container">
            <h1 class="error-title">Eroare la generarea PDF</h1>
            <p class="error-description"><?php echo htmlspecialchars($error); ?></p>
            <?php if (isset($errorCode)): ?>
                <div class="error-code">Cod eroare: <?php echo htmlspecialchars($errorCode); ?></div>
            <?php endif; ?>
            <a href="javascript:history.back()" class="back-link">← Înapoi</a>
        </div>
    </body>
    </html>
    <?php
    exit;
}

/**
 * Generează PDF folosind TCPDF cu visual parity față de interfața web
 */
function generatePDFWithTCPDF($dosar, $numarDosar, $institutie) {
    // Creăm o instanță TCPDF
    $pdf = new TCPDF(PDF_PAGE_ORIENTATION, PDF_UNIT, PDF_PAGE_FORMAT, true, 'UTF-8', false);

    // Setăm informațiile documentului
    $pdf->SetCreator(PDF_CREATOR);
    $pdf->SetAuthor('Portal Judiciar');
    $pdf->SetTitle("Detalii dosar {$numarDosar}");
    $pdf->SetSubject('Informații dosar judiciar');
    $pdf->SetKeywords('dosar, judiciar, instanță, România');

    // Setăm marginile (2cm = 20mm)
    $pdf->SetMargins(20, 20, 20);
    $pdf->SetHeaderMargin(10);
    $pdf->SetFooterMargin(15);

    // Setăm auto page breaks
    $pdf->SetAutoPageBreak(TRUE, 25);

    // Setăm factorul de scalare pentru imagini
    $pdf->setImageScale(PDF_IMAGE_SCALE_RATIO);

    // Setăm fontul implicit
    $pdf->SetFont('helvetica', '', 10);

    // Adăugăm prima pagină
    $pdf->AddPage();

    // Generăm conținutul HTML
    $html = generateHTMLContent($dosar, $numarDosar, $institutie);

    // Scriem HTML în PDF
    $pdf->writeHTML($html, true, false, true, false, '');

    // Returnăm PDF-ul ca string
    return $pdf->Output('', 'S');
}

/**
 * Generează conținutul HTML pentru PDF cu stiluri inline pentru visual parity
 */
function generateHTMLContent($dosar, $numarDosar, $institutie) {
    // Parametrii $numarDosar și $institutie sunt păstrați pentru compatibilitate și extensibilitate viitoare
    // Obținem lista instanțelor
    $institutii = getInstanteList();
    $numeInstanta = $institutii[$dosar->institutie] ?? $dosar->institutie;

    $html = '
    <style>
        body { font-family: helvetica, sans-serif; font-size: 10pt; line-height: 1.4; color: #333; }
        h1 { color: #007bff; font-size: 18pt; margin-bottom: 10px; text-align: center; }
        h2 { color: #007bff; font-size: 14pt; margin-top: 20px; margin-bottom: 10px; border-bottom: 1px solid #e9ecef; padding-bottom: 5px; }
        h3 { color: #495057; font-size: 12pt; margin-top: 15px; margin-bottom: 8px; }
        .header-info { background-color: #f8f9fa; padding: 15px; border-radius: 5px; margin-bottom: 20px; }
        .info-row { margin-bottom: 8px; }
        .info-label { font-weight: bold; color: #495057; }
        .info-value { color: #212529; }
        table { width: 100%; border-collapse: collapse; margin-bottom: 20px; }
        th { background-color: #007bff; color: white; padding: 8px; text-align: left; font-weight: bold; }
        td { padding: 8px; border-bottom: 1px solid #dee2e6; }
        tr:nth-child(even) { background-color: #f8f9fa; }
        .badge { background-color: #007bff; color: white; padding: 3px 8px; border-radius: 3px; font-size: 8pt; }
        .badge-info { background-color: #17a2b8; }
        .text-muted { color: #6c757d; }
        .solution-detail { background-color: #f8f9fa; padding: 10px; border-radius: 4px; margin-top: 5px; font-size: 9pt; }
        .footer-info { text-align: center; font-size: 8pt; color: #6c757d; margin-top: 30px; border-top: 1px solid #e9ecef; padding-top: 10px; }
    </style>';

    $html .= '<h1>Detalii dosar: ' . htmlspecialchars($dosar->numar) . '</h1>';

    // Informații generale
    $html .= '<div class="header-info">';
    $html .= '<h2>Informații generale</h2>';
    
    if (!empty($dosar->numarVechi)) {
        $html .= '<div class="info-row"><span class="info-label">Număr vechi:</span> <span class="info-value">' . htmlspecialchars($dosar->numarVechi) . '</span></div>';
    }
    
    $html .= '<div class="info-row"><span class="info-label">Instanță:</span> <span class="info-value">' . htmlspecialchars($numeInstanta) . '</span></div>';
    
    if (!empty($dosar->departament)) {
        $html .= '<div class="info-row"><span class="info-label">Departament/Secție:</span> <span class="info-value">' . htmlspecialchars($dosar->departament) . '</span></div>';
    }
    
    $html .= '<div class="info-row"><span class="info-label">Data:</span> <span class="info-value">' . htmlspecialchars($dosar->data) . '</span></div>';
    $html .= '<div class="info-row"><span class="info-label">Obiect:</span> <span class="info-value">' . htmlspecialchars($dosar->obiect) . '</span></div>';
    $html .= '<div class="info-row"><span class="info-label">Stadiu procesual:</span> <span class="info-value">' . htmlspecialchars($dosar->stadiuProcesualNume) . '</span></div>';
    $html .= '<div class="info-row"><span class="info-label">Categorie caz:</span> <span class="info-value">' . htmlspecialchars($dosar->categorieCazNume) . '</span></div>';
    $html .= '<div class="info-row"><span class="info-label">Data ultimei modificări:</span> <span class="info-value">' . htmlspecialchars($dosar->dataModificare) . '</span></div>';
    $html .= '</div>';

    // Părțile implicate
    if (!empty($dosar->parti)) {
        $html .= '<h2>Părți implicate (' . count($dosar->parti) . ')</h2>';
        $html .= '<table>';
        $html .= '<thead><tr><th>Nume</th><th>Calitate</th><th>Informații suplimentare</th></tr></thead>';
        $html .= '<tbody>';
        
        foreach ($dosar->parti as $parte) {
            // Verificăm dacă partea este declaratoare în vreo cale de atac
            $esteDeclaratoare = false;
            $tipuriCaleAtac = [];
            
            if (!empty($dosar->caiAtac)) {
                foreach ($dosar->caiAtac as $caleAtac) {
                    if (!empty($caleAtac['parteDeclaratoare']) &&
                        (stripos($parte['nume'], $caleAtac['parteDeclaratoare']) !== false ||
                        stripos($caleAtac['parteDeclaratoare'], $parte['nume']) !== false)) {
                        $esteDeclaratoare = true;
                        if (!empty($caleAtac['tipCaleAtac']) && !in_array($caleAtac['tipCaleAtac'], $tipuriCaleAtac)) {
                            $tipuriCaleAtac[] = $caleAtac['tipCaleAtac'];
                        }
                    }
                }
            }
            
            $rowStyle = $esteDeclaratoare ? ' style="background-color: #d1ecf1;"' : '';
            $html .= '<tr' . $rowStyle . '>';
            $html .= '<td>' . htmlspecialchars($parte['nume']) . '</td>';
            $html .= '<td>' . (!empty($parte['calitate']) ? htmlspecialchars($parte['calitate']) : '<span class="text-muted">-</span>') . '</td>';
            $html .= '<td>';
            
            if ($esteDeclaratoare) {
                $html .= '<span class="badge">Parte declaratoare</span>';
                if (!empty($tipuriCaleAtac)) {
                    foreach ($tipuriCaleAtac as $tip) {
                        $html .= ' <span class="badge badge-info">' . htmlspecialchars($tip) . '</span>';
                    }
                }
            } else {
                $html .= '<span class="text-muted">-</span>';
            }
            
            $html .= '</td>';
            $html .= '</tr>';
        }
        
        $html .= '</tbody></table>';
    }

    // Ședințele de judecată
    if (!empty($dosar->sedinte)) {
        $html .= '<h2>Ședințe de judecată (' . count($dosar->sedinte) . ')</h2>';
        $html .= '<table>';
        $html .= '<thead><tr><th style="width: 15%;">Data ședință</th><th style="width: 15%;">Data pronunțare</th><th style="width: 10%;">Ora</th><th style="width: 15%;">Complet</th><th style="width: 35%;">Soluție</th><th style="width: 10%;">Document</th></tr></thead>';
        $html .= '<tbody>';

        foreach ($dosar->sedinte as $sedinta) {
            $html .= '<tr>';
            $html .= '<td>' . htmlspecialchars($sedinta['data']) . '</td>';
            $html .= '<td>' . (!empty($sedinta['dataPronuntare']) ? htmlspecialchars($sedinta['dataPronuntare']) : '<span class="text-muted">-</span>') . '</td>';
            $html .= '<td>' . (!empty($sedinta['ora']) ? htmlspecialchars($sedinta['ora']) : '<span class="text-muted">-</span>') . '</td>';
            $html .= '<td>' . (!empty($sedinta['complet']) ? htmlspecialchars($sedinta['complet']) : '<span class="text-muted">-</span>') . '</td>';

            // Soluția - cu handling special pentru text lung
            $html .= '<td>';
            if (!empty($sedinta['solutie'])) {
                $solutie = htmlspecialchars($sedinta['solutie']);
                if (strlen($solutie) > 100) {
                    // Pentru soluții lungi, afișăm primele 100 de caractere și adăugăm detaliile în secțiune separată
                    $html .= substr($solutie, 0, 100) . '...';
                    if (!empty($sedinta['solutieSumar']) && $sedinta['solutie'] != $sedinta['solutieSumar']) {
                        $html .= '<br><small class="text-muted">' . htmlspecialchars($sedinta['solutieSumar']) . '</small>';
                    }
                } else {
                    $html .= $solutie;
                    if (!empty($sedinta['solutieSumar']) && $sedinta['solutie'] != $sedinta['solutieSumar']) {
                        $html .= '<br><small class="text-muted">' . htmlspecialchars($sedinta['solutieSumar']) . '</small>';
                    }
                }
            } elseif (!empty($sedinta['solutieSumar'])) {
                $html .= htmlspecialchars($sedinta['solutieSumar']);
            } else {
                $html .= '<span class="text-muted">Nedisponibilă</span>';
            }
            $html .= '</td>';

            // Document
            $html .= '<td>';
            if (!empty($sedinta['numarDocument'])) {
                $html .= 'Nr: ' . htmlspecialchars($sedinta['numarDocument']);
                if (!empty($sedinta['dataDocument'])) {
                    $html .= '<br><small>Data: ' . htmlspecialchars($sedinta['dataDocument']) . '</small>';
                }
                if (!empty($sedinta['documentSedinta'])) {
                    $html .= '<br><small>Tip: ' . htmlspecialchars($sedinta['documentSedinta']) . '</small>';
                }
            } else {
                $html .= '<span class="text-muted">-</span>';
            }
            $html .= '</td>';

            $html .= '</tr>';

            // Adăugăm soluția detaliată dacă este prea lungă
            if (!empty($sedinta['solutie']) && strlen($sedinta['solutie']) > 100) {
                $html .= '<tr><td colspan="6" class="solution-detail">';
                $html .= '<strong>Soluție detaliată:</strong><br>';
                $html .= nl2br(htmlspecialchars($sedinta['solutie']));
                $html .= '</td></tr>';
            }
        }

        $html .= '</tbody></table>';
    }

    // Căile de atac
    if (!empty($dosar->caiAtac)) {
        $html .= '<h2>Căi de atac (' . count($dosar->caiAtac) . ')</h2>';
        $html .= '<table>';
        $html .= '<thead><tr><th>Data declarare</th><th>Parte declaratoare</th><th>Tip cale atac</th><th>Dosar instanță superioară</th></tr></thead>';
        $html .= '<tbody>';

        foreach ($dosar->caiAtac as $caleAtac) {
            $html .= '<tr>';
            $html .= '<td>' . (!empty($caleAtac['dataDeclarare']) ? htmlspecialchars($caleAtac['dataDeclarare']) : '<span class="text-muted">-</span>') . '</td>';
            $html .= '<td>' . (!empty($caleAtac['parteDeclaratoare']) ? htmlspecialchars($caleAtac['parteDeclaratoare']) : '<span class="text-muted">-</span>') . '</td>';
            $html .= '<td>';
            if (!empty($caleAtac['tipCaleAtac'])) {
                $html .= '<span class="badge badge-info">' . htmlspecialchars($caleAtac['tipCaleAtac']) . '</span>';
            } else {
                $html .= '<span class="text-muted">-</span>';
            }
            $html .= '</td>';
            $html .= '<td>';
            if (!empty($caleAtac['numarDosarInstantaSuperior'])) {
                $html .= htmlspecialchars($caleAtac['numarDosarInstantaSuperior']);
                if (!empty($caleAtac['instantaSuperior'])) {
                    $instantaSuperior = $institutii[$caleAtac['instantaSuperior']] ?? $caleAtac['instantaSuperior'];
                    $html .= '<br><small>Instanță: ' . htmlspecialchars($instantaSuperior) . '</small>';
                }
            } elseif (!empty($caleAtac['instantaSuperior'])) {
                $instantaSuperior = $institutii[$caleAtac['instantaSuperior']] ?? $caleAtac['instantaSuperior'];
                $html .= 'Instanță: ' . htmlspecialchars($instantaSuperior) . '<br><small class="text-muted">Număr dosar necunoscut</small>';
            } else {
                $html .= '<span class="text-muted">-</span>';
            }
            $html .= '</td>';
            $html .= '</tr>';
        }

        $html .= '</tbody></table>';
    }

    // Footer cu informații
    $html .= '<div class="footer-info">';
    $html .= '<p>Document generat de Portalul Dosare Judecătorești</p>';
    $html .= '<p>Informațiile sunt preluate în timp real de la sistemul oficial al instanțelor de judecată</p>';
    $html .= '<p>Generat la data: ' . date('d.m.Y H:i') . '</p>';
    $html .= '</div>';

    return $html;
}

/**
 * Funcție mock pentru obținerea listei de instanțe
 * În implementarea finală, aceasta va fi înlocuită cu funcția reală
 */
function getInstanteList() {
    return [
        'TBBU' => 'Tribunalul București',
        'JDBU' => 'Judecătoria București',
        'CAB' => 'Curtea de Apel București',
        'ICCJ' => 'Înalta Curte de Casație și Justiție'
    ];
}
?>
