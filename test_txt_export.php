<?php
/**
 * Test script pentru verificarea exportului TXT
 */

// Setăm encoding-ul pentru PHP
if (function_exists('mb_internal_encoding')) {
    mb_internal_encoding('UTF-8');
}

// Test data cu caractere românești
$testResults = [
    (object)[
        'numar' => '123/2024',
        'institutie' => 'JUDECATORIA_BUCURESTI',
        'obiect' => 'Contract de vânzare-cumpărare',
        'stadiuProcesual' => 'În curs de judecată',
        'dataInregistrarii' => '2024-03-15',
        'categorieCaz' => 'Civil',
        'dataUltimaModificare' => '2024-03-20',
        'parti' => [
            ['nume' => 'SC EXEMPLU SRL', 'calitate' => 'Pârât']
        ]
    ],
    (object)[
        'numar' => '456/2024',
        'institutie' => 'TRIBUNALUL_IASI',
        'obiect' => 'Acțiune în pretenții',
        'stadiuProcesual' => 'Finalizat',
        'dataInregistrarii' => '2024-03-20',
        'categorieCaz' => 'Civil',
        'dataUltimaModificare' => '2024-03-25',
        'parti' => [
            ['nume' => 'Ion Popescu', 'calitate' => 'Reclamant']
        ]
    ]
];

// Simulăm funcțiile necesare
function cleanForTxt($text) {
    if (empty($text)) return '-';
    $text = (string) $text;
    $text = str_replace(["\r\n", "\r", "\n", "\t"], ' ', $text);
    $text = strip_tags($text);
    $text = preg_replace('/\s+/u', ' ', $text);
    $text = trim($text);
    return $text ?: '-';
}

function formatDateForTxt($date) {
    if (empty($date)) return '-';
    try {
        $dateObj = new DateTime($date);
        return $dateObj->format('d.m.Y');
    } catch (Exception $e) {
        return cleanForTxt($date);
    }
}

function getInstanteName($institutie) {
    $institutii = [
        'JUDECATORIA_BUCURESTI' => 'Judecătoria București',
        'TRIBUNALUL_IASI' => 'Tribunalul Iași'
    ];
    return $institutii[$institutie] ?? $institutie;
}

function buildCleanDetailUrl($numar, $institutie) {
    return 'https://portal.just.ro/detalii_dosar.php?numar=' . urlencode($numar) . '&institutie=' . urlencode($institutie);
}

// Generăm fișierul TXT de test
header('Content-Type: text/plain; charset=UTF-8');
header('Content-Disposition: attachment; filename="test_export.txt"');

echo "REZULTATE CĂUTARE DOSARE JUDICIARE\n";
echo "===================================\n";
echo "Data export: " . date('d.m.Y H:i:s') . "\n";
echo "Total rezultate: " . count($testResults) . "\n\n";

$counter = 1;

foreach ($testResults as $dosar) {
    $relevantParty = $dosar->parti[0] ?? null;
    
    echo "DOSAR #{$counter}\n";
    echo "----------------\n";
    echo "Număr dosar: " . cleanForTxt($dosar->numar) . "\n";
    echo "Instanță: " . cleanForTxt(getInstanteName($dosar->institutie)) . "\n";
    echo "Obiect: " . cleanForTxt($dosar->obiect) . "\n";
    echo "Stadiu procesual: " . cleanForTxt($dosar->stadiuProcesual) . "\n";
    echo "Data înregistrării: " . formatDateForTxt($dosar->dataInregistrarii) . "\n";
    echo "Categorie caz: " . cleanForTxt($dosar->categorieCaz) . "\n";
    echo "Data ultimei modificări: " . formatDateForTxt($dosar->dataUltimaModificare) . "\n";
    echo "Nume parte relevantă: " . cleanForTxt($relevantParty ? $relevantParty['nume'] : '') . "\n";
    echo "Calitate parte: " . cleanForTxt($relevantParty ? $relevantParty['calitate'] : '') . "\n";
    echo "Link detalii: " . buildCleanDetailUrl($dosar->numar, $dosar->institutie) . "\n";
    echo "\n";
    
    $counter++;
}

echo "===================================\n";
echo "Sfârșit export - Total: " . ($counter - 1) . " dosare\n";
?>
