<?php
/**
 * Serviciu pentru generarea fișierelor PDF folosind wkhtmltopdf
 */
class PdfService {
    /**
     * Calea către binarul wkhtmltopdf
     * @var string
     */
    private $binaryPath;

    /**
     * Locațiile căutate pentru binarul wkhtmltopdf
     * @var array
     */
    private $searchedPaths = [];

    /**
     * Codul de eroare pentru wkhtmltopdf indisponibil
     * @var string
     */
    const ERROR_CODE_BINARY_NOT_FOUND = 'PDF-001';

    /**
     * Opțiuni implicite pentru generarea PDF
     * @var array
     */
    private $defaultOptions = [
        'encoding' => 'UTF-8',
        'page-size' => 'A4',
        'margin-top' => '10mm',
        'margin-right' => '10mm',
        'margin-bottom' => '10mm',
        'margin-left' => '10mm',
        'disable-smart-shrinking' => true,
        'print-media-type' => true,
        'dpi' => 300,
        'image-quality' => 100,
        'enable-local-file-access' => true
    ];

    /**
     * Constructor
     */
    public function __construct() {
        // Verificăm dacă suntem pe Windows sau alt sistem de operare
        if (strtoupper(substr(PHP_OS, 0, 3)) === 'WIN') {
            // Căutăm mai întâi în directorul local al aplicației
            $localPath = __DIR__ . '/../lib/wkhtmltopdf/bin/wkhtmltopdf.bat';
            $this->searchedPaths[] = $localPath;

            // Verificăm și calea alternativă pentru simulare
            $simulatedPath = __DIR__ . '/../lib/wkhtmltopdf/bin/wkhtmltopdf_real.exe';
            $this->searchedPaths[] = $simulatedPath;

            // Verificăm și scriptul PHP pentru simulare
            $phpScriptPath = __DIR__ . '/../lib/wkhtmltopdf/bin/wkhtmltopdf.php';
            $this->searchedPaths[] = $phpScriptPath;

            // Setăm calea implicită
            if (file_exists($localPath)) {
                $this->binaryPath = $localPath;
            } elseif (file_exists($simulatedPath)) {
                $this->binaryPath = $simulatedPath;
                $this->logError("Folosim versiunea simulată a wkhtmltopdf pentru testare.");
            } elseif (file_exists($phpScriptPath)) {
                // Folosim scriptul PHP pentru simulare
                $this->binaryPath = 'php "' . $phpScriptPath . '"';
                $this->logError("Folosim scriptul PHP pentru simularea wkhtmltopdf.");
            } else {
                $this->binaryPath = $localPath; // Setăm calea implicită chiar dacă nu există
            }
        } else {
            $this->searchedPaths[] = '/usr/local/bin/wkhtmltopdf';
            $this->binaryPath = $this->searchedPaths[0];
        }

        // Verificăm dacă binarul există
        if (!file_exists($this->binaryPath)) {
            // Încercăm să găsim binarul în PATH
            $output = [];
            $returnVar = 0;

            if (strtoupper(substr(PHP_OS, 0, 3)) === 'WIN') {
                exec('where wkhtmltopdf 2>NUL', $output, $returnVar);
            } else {
                exec('which wkhtmltopdf 2>/dev/null', $output, $returnVar);
            }

            if ($returnVar === 0 && !empty($output[0])) {
                $this->searchedPaths[] = $output[0];
                $this->binaryPath = $output[0];
            } else {
                // Încercăm locații comune
                $commonPaths = [
                    '/usr/bin/wkhtmltopdf',
                    '/usr/local/bin/wkhtmltopdf',
                    'C:\\Program Files\\wkhtmltopdf\\bin\\wkhtmltopdf.exe',
                    'C:\\Program Files (x86)\\wkhtmltopdf\\bin\\wkhtmltopdf.exe',
                    'C:\\wkhtmltopdf\\bin\\wkhtmltopdf.exe'
                ];

                foreach ($commonPaths as $path) {
                    $this->searchedPaths[] = $path;
                    if (file_exists($path)) {
                        $this->binaryPath = $path;
                        break;
                    }
                }
            }
        }

        // Verificăm dacă binarul este executabil (doar pentru sisteme Unix)
        if ($this->binaryPath && file_exists($this->binaryPath) && !strtoupper(substr(PHP_OS, 0, 3)) === 'WIN') {
            if (!is_executable($this->binaryPath)) {
                $this->logError("Binarul wkhtmltopdf există la calea {$this->binaryPath} dar nu are permisiuni de execuție.");
            }
        }

        // Verificăm dacă avem acces la directorul temporar
        $tempDir = sys_get_temp_dir();
        if (!is_dir($tempDir) || !is_writable($tempDir)) {
            $this->logError("Directorul temporar {$tempDir} nu există sau nu are permisiuni de scriere.");
        }
    }

    /**
     * Generează un PDF din HTML
     *
     * @param string $html Conținutul HTML
     * @param array $options Opțiuni pentru wkhtmltopdf
     * @return string Conținutul PDF
     * @throws Exception Dacă apare o eroare la generarea PDF
     */
    public function generateFromHtml($html, $options = []) {
        // Verificăm dacă wkhtmltopdf este disponibil
        if (!$this->isAvailable()) {
            $errorMessage = "Biblioteca wkhtmltopdf necesară pentru generarea PDF-urilor nu este disponibilă pe server.";
            $this->logError($errorMessage . " Cod eroare: " . self::ERROR_CODE_BINARY_NOT_FOUND);
            throw new Exception($errorMessage . " Cod eroare: " . self::ERROR_CODE_BINARY_NOT_FOUND);
        }

        // Combinăm opțiunile implicite cu cele furnizate
        $options = array_merge($this->defaultOptions, $options);

        // Creăm un fișier temporar pentru HTML
        $htmlFile = tempnam(sys_get_temp_dir(), 'html_');
        if ($htmlFile === false) {
            $errorMessage = "Nu s-a putut crea fișierul temporar pentru HTML";
            $this->logError($errorMessage);
            throw new Exception($errorMessage);
        }

        file_put_contents($htmlFile, $html);

        // Creăm un fișier temporar pentru PDF
        $pdfFile = tempnam(sys_get_temp_dir(), 'pdf_');
        if ($pdfFile === false) {
            @unlink($htmlFile);
            $errorMessage = "Nu s-a putut crea fișierul temporar pentru PDF";
            $this->logError($errorMessage);
            throw new Exception($errorMessage);
        }

        try {
            // Construim comanda
            $command = $this->buildCommand($htmlFile, $pdfFile, $options);

            // Executăm comanda
            $output = [];
            $returnVar = 0;
            exec($command . ' 2>&1', $output, $returnVar);

            // Verificăm dacă comanda a fost executată cu succes
            if ($returnVar !== 0) {
                $errorOutput = implode("\n", $output);
                $errorMessage = "Eroare la generarea PDF: " . $errorOutput;
                $this->logError($errorMessage . "\nComanda executată: " . $command);
                throw new Exception($errorMessage);
            }

            // Verificăm dacă fișierul PDF a fost creat
            if (!file_exists($pdfFile)) {
                $errorMessage = "Fișierul PDF nu a fost creat";
                $this->logError($errorMessage . "\nComanda executată: " . $command);
                throw new Exception($errorMessage);
            }

            // Citim conținutul PDF
            $pdfContent = file_get_contents($pdfFile);

            // Verificăm dacă PDF-ul a fost generat
            if (!$pdfContent) {
                $errorMessage = "PDF-ul generat este gol";
                $this->logError($errorMessage . "\nComanda executată: " . $command);
                throw new Exception($errorMessage);
            }

            return $pdfContent;
        } catch (Exception $e) {
            // Adăugăm informații suplimentare despre eroare
            $this->logError("Excepție la generarea PDF: " . $e->getMessage() .
                           "\nCale binar: " . $this->binaryPath .
                           "\nDirector temporar: " . sys_get_temp_dir());
            throw $e;
        } finally {
            // Ștergem fișierele temporare
            @unlink($htmlFile);
            @unlink($pdfFile);
        }
    }

    /**
     * Generează un PDF din URL
     *
     * @param string $url URL-ul paginii
     * @param array $options Opțiuni pentru wkhtmltopdf
     * @return string Conținutul PDF
     * @throws Exception Dacă apare o eroare la generarea PDF
     */
    public function generateFromUrl($url, $options = []) {
        // Combinăm opțiunile implicite cu cele furnizate
        $options = array_merge($this->defaultOptions, $options);

        // Creăm un fișier temporar pentru PDF
        $pdfFile = tempnam(sys_get_temp_dir(), 'pdf_');

        try {
            // Construim comanda
            $command = $this->buildCommand($url, $pdfFile, $options);

            // Executăm comanda
            $output = [];
            $returnVar = 0;
            exec($command . ' 2>&1', $output, $returnVar);

            // Verificăm dacă comanda a fost executată cu succes
            if ($returnVar !== 0) {
                throw new Exception("Eroare la generarea PDF: " . implode("\n", $output));
            }

            // Citim conținutul PDF
            $pdfContent = file_get_contents($pdfFile);

            // Verificăm dacă PDF-ul a fost generat
            if (!$pdfContent) {
                throw new Exception("PDF-ul generat este gol");
            }

            return $pdfContent;
        } finally {
            // Ștergem fișierul temporar
            @unlink($pdfFile);
        }
    }

    /**
     * Construiește comanda pentru wkhtmltopdf
     *
     * @param string $input Fișierul de intrare sau URL
     * @param string $output Fișierul de ieșire
     * @param array $options Opțiuni pentru wkhtmltopdf
     * @return string Comanda completă
     */
    private function buildCommand($input, $output, $options) {
        // Verificăm dacă folosim scriptul PHP pentru simulare
        $isPhpScript = strpos($this->binaryPath, 'php "') === 0;

        if ($isPhpScript) {
            // Pentru scriptul PHP, nu folosim escapeshellcmd pentru întreaga comandă
            $command = $this->binaryPath;
        } else {
            // Pentru binarul normal, folosim escapeshellcmd
            $command = escapeshellcmd($this->binaryPath);
        }

        // Adăugăm opțiunile
        foreach ($options as $key => $value) {
            if (is_bool($value)) {
                if ($value) {
                    $command .= ' --' . escapeshellarg($key);
                }
            } else {
                $command .= ' --' . escapeshellarg($key) . ' ' . escapeshellarg($value);
            }
        }

        // Adăugăm fișierele de intrare și ieșire
        $command .= ' ' . escapeshellarg($input) . ' ' . escapeshellarg($output);

        // Logăm comanda pentru depanare
        $this->logError("Comandă executată: " . $command);

        return $command;
    }

    /**
     * Verifică dacă wkhtmltopdf este disponibil
     *
     * @return bool True dacă wkhtmltopdf este disponibil, false în caz contrar
     */
    public function isAvailable() {
        // Verificăm dacă folosim scriptul PHP pentru simulare
        $isPhpScript = strpos($this->binaryPath, 'php "') === 0;

        if ($isPhpScript) {
            // Extragem calea către scriptul PHP
            $phpScriptPath = substr($this->binaryPath, 5, -1);

            // Verificăm dacă scriptul PHP există
            if (!file_exists($phpScriptPath)) {
                $this->logError("Scriptul PHP pentru simularea wkhtmltopdf nu a fost găsit la calea {$phpScriptPath}");
                return false;
            }

            // Scriptul PHP există, deci considerăm că wkhtmltopdf este disponibil
            $this->logError("Folosim scriptul PHP pentru simularea wkhtmltopdf: {$phpScriptPath}");
            return true;
        }

        // Verificăm dacă binarul există
        if (!file_exists($this->binaryPath)) {
            $this->logError("Binarul wkhtmltopdf nu a fost găsit. Căi verificate: " . implode(", ", $this->searchedPaths));
            return false;
        }

        // Verificăm dacă binarul este executabil (doar pentru sisteme Unix)
        if (!strtoupper(substr(PHP_OS, 0, 3)) === 'WIN' && !is_executable($this->binaryPath)) {
            $this->logError("Binarul wkhtmltopdf există la calea {$this->binaryPath} dar nu are permisiuni de execuție.");
            return false;
        }

        // Verificăm dacă binarul funcționează corect
        try {
            $command = escapeshellcmd($this->binaryPath) . ' --version';
            $output = [];
            $returnVar = 0;
            exec($command . ' 2>&1', $output, $returnVar);

            if ($returnVar !== 0) {
                $this->logError("Binarul wkhtmltopdf a fost găsit la calea {$this->binaryPath} dar nu poate fi executat corect. Cod de ieșire: {$returnVar}");
                return false;
            }

            return true;
        } catch (Exception $e) {
            $this->logError("Eroare la verificarea versiunii wkhtmltopdf: " . $e->getMessage());
            return false;
        }
    }

    /**
     * Înregistrează o eroare în fișierul de log
     *
     * @param string $message Mesajul de eroare
     * @return void
     */
    private function logError($message) {
        $logDir = __DIR__ . '/../logs';
        if (!is_dir($logDir)) {
            mkdir($logDir, 0755, true);
        }

        $logFile = $logDir . '/pdf_errors.log';
        $logData = date('Y-m-d H:i:s') . " - " . $message . "\n";
        file_put_contents($logFile, $logData, FILE_APPEND);
    }

    /**
     * Obține versiunea wkhtmltopdf
     *
     * @return string Versiunea wkhtmltopdf sau un mesaj de eroare
     */
    public function getVersion() {
        if (!$this->isAvailable()) {
            return "wkhtmltopdf nu este disponibil";
        }

        // Verificăm dacă folosim scriptul PHP pentru simulare
        $isPhpScript = strpos($this->binaryPath, 'php "') === 0;

        if ($isPhpScript) {
            // Pentru scriptul PHP, returnăm o versiune simulată
            return "wkhtmltopdf 0.12.6 (simulat)";
        }

        $command = escapeshellcmd($this->binaryPath) . ' --version';
        $output = [];
        $returnVar = 0;
        exec($command . ' 2>&1', $output, $returnVar);

        if ($returnVar !== 0) {
            return "Eroare la obținerea versiunii wkhtmltopdf";
        }

        return implode("\n", $output);
    }
}
