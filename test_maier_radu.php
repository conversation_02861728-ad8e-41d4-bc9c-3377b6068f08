<?php
/**
 * Test script for MAIER RADU party matching functionality
 * This script will test the specific case mentioned in the requirements
 */

// Include necessary files
require_once 'includes/config.php';
require_once 'services/DosarService.php';

// Test parameters
$searchTerm = 'MAIER RADU';
$searchType = 'numeParte';
$expectedDosar = '12748/211/2019';

echo "<h1>Test: Enhanced Party Matching for MAIER RADU</h1>";
echo "<p><strong>Search Term:</strong> {$searchTerm}</p>";
echo "<p><strong>Search Type:</strong> {$searchType}</p>";
echo "<p><strong>Expected Case:</strong> {$expectedDosar}</p>";
echo "<hr>";

try {
    // Initialize the service
    $dosarService = new DosarService();
    
    // Search parameters
    $searchParams = [
        'numarDosar' => '',
        'institutie' => null,
        'obiectDosar' => '',
        'numeParte' => $searchTerm,
        'dataStart' => null,
        'dataStop' => null,
        'dataUltimaModificareStart' => null,
        'dataUltimaModificareStop' => null
    ];
    
    echo "<h2>Executing Search...</h2>";
    $results = $dosarService->searchDosare($searchParams);
    
    echo "<p><strong>Total Results Found:</strong> " . count($results) . "</p>";
    
    // Look for the specific case
    $targetCase = null;
    foreach ($results as $dosar) {
        if (($dosar->numar ?? '') === $expectedDosar) {
            $targetCase = $dosar;
            break;
        }
    }
    
    if ($targetCase) {
        echo "<h2>✅ Target Case Found: {$expectedDosar}</h2>";
        
        // Display case details
        echo "<h3>Case Details:</h3>";
        echo "<ul>";
        echo "<li><strong>Număr:</strong> " . htmlspecialchars($targetCase->numar ?? '') . "</li>";
        echo "<li><strong>Instanță:</strong> " . htmlspecialchars($targetCase->institutie ?? '') . "</li>";
        echo "<li><strong>Obiect:</strong> " . htmlspecialchars($targetCase->obiect ?? '') . "</li>";
        echo "<li><strong>Data:</strong> " . htmlspecialchars($targetCase->data ?? '') . "</li>";
        echo "</ul>";
        
        // Display parties
        echo "<h3>Parties in Case:</h3>";
        $parti = $targetCase->parti ?? [];
        
        if (is_array($parti) && !empty($parti)) {
            echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
            echo "<tr><th>Index</th><th>Nume</th><th>Calitate</th><th>Match Test</th></tr>";
            
            foreach ($parti as $index => $party) {
                $nume = '';
                $calitate = '';
                
                if (is_array($party)) {
                    $nume = $party['nume'] ?? '';
                    $calitate = $party['calitate'] ?? '';
                } elseif (is_object($party)) {
                    $nume = $party->nume ?? '';
                    $calitate = $party->calitate ?? '';
                }
                
                // Test if this party matches our search term
                $normalizedPartyName = mb_strtolower(trim($nume), 'UTF-8');
                $normalizedSearchTerm = mb_strtolower(trim($searchTerm), 'UTF-8');
                $isMatch = mb_strpos($normalizedPartyName, $normalizedSearchTerm, 0, 'UTF-8') !== false;
                
                $matchStatus = $isMatch ? "✅ MATCH" : "❌ No match";
                $rowStyle = $isMatch ? "background-color: #d4edda;" : "";
                
                echo "<tr style='{$rowStyle}'>";
                echo "<td>{$index}</td>";
                echo "<td>" . htmlspecialchars($nume) . "</td>";
                echo "<td>" . htmlspecialchars($calitate) . "</td>";
                echo "<td>{$matchStatus}</td>";
                echo "</tr>";
            }
            echo "</table>";
            
            // Test the enhanced matching functions
            echo "<h3>Enhanced Matching Function Tests:</h3>";
            
            // Include the functions from bulk_search.php
            include_once 'bulk_search_functions.php';
            
            if (function_exists('findMatchingParty')) {
                $matchingParty = findMatchingParty($parti, $searchTerm);
                
                if ($matchingParty) {
                    $matchedName = '';
                    $matchedQuality = '';
                    
                    if (is_array($matchingParty)) {
                        $matchedName = $matchingParty['nume'] ?? '';
                        $matchedQuality = $matchingParty['calitate'] ?? '';
                    }
                    
                    echo "<p><strong>✅ Matching Party Found:</strong></p>";
                    echo "<ul>";
                    echo "<li><strong>Name:</strong> " . htmlspecialchars($matchedName) . "</li>";
                    echo "<li><strong>Quality:</strong> " . htmlspecialchars($matchedQuality) . "</li>";
                    echo "</ul>";
                    
                    // Test highlighting
                    if (function_exists('highlightMatchingPartyName')) {
                        $highlighted = highlightMatchingPartyName($matchedName, $searchTerm, $searchType);
                        echo "<p><strong>Highlighted Result:</strong> {$highlighted}</p>";
                    }
                } else {
                    echo "<p><strong>❌ No matching party found by enhanced function</strong></p>";
                }
            } else {
                echo "<p><strong>⚠️ Enhanced matching functions not available</strong></p>";
            }
            
        } else {
            echo "<p><strong>❌ No parties found in case or parties data is not in expected format</strong></p>";
            echo "<p>Parties data type: " . gettype($parti) . "</p>";
            echo "<p>Parties data: <pre>" . print_r($parti, true) . "</pre></p>";
        }
        
    } else {
        echo "<h2>❌ Target Case NOT Found: {$expectedDosar}</h2>";
        echo "<p>The case might not exist or might not be returned by the search.</p>";
        
        if (!empty($results)) {
            echo "<h3>Available Cases (first 5):</h3>";
            echo "<ul>";
            foreach (array_slice($results, 0, 5) as $dosar) {
                echo "<li>" . htmlspecialchars($dosar->numar ?? 'Unknown') . " - " . htmlspecialchars($dosar->institutie ?? 'Unknown') . "</li>";
            }
            echo "</ul>";
        }
    }
    
} catch (Exception $e) {
    echo "<h2>❌ Error during search:</h2>";
    echo "<p>" . htmlspecialchars($e->getMessage()) . "</p>";
}

echo "<hr>";
echo "<p><a href='bulk_search.php'>← Back to Bulk Search</a></p>";
?>
