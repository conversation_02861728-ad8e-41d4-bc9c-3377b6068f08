<?php
/**
 * Comprehensive test for the specific failing filtered search
 */

require_once 'includes/config.php';
require_once 'includes/functions.php';
require_once 'src/Services/DosarService.php';

echo "<h1>🔍 Filtered Search Debug Test</h1>\n";
echo "<style>
    .test-section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
    .success { background-color: #d4edda; border-color: #c3e6cb; }
    .error { background-color: #f8d7da; border-color: #f5c6cb; }
    .warning { background-color: #fff3cd; border-color: #ffeaa7; }
    .info { background-color: #d1ecf1; border-color: #bee5eb; }
    pre { background: #f8f9fa; padding: 10px; border-radius: 3px; overflow-x: auto; max-height: 300px; overflow-y: auto; }
    .result-item { border: 1px solid #ddd; margin: 10px 0; padding: 10px; border-radius: 5px; }
</style>\n";

// Test the specific failing parameters
$testParams = [
    'institutie' => '',
    'categorieInstanta' => 'curte_apel',
    'categorieCaz' => 'munca',
    'dataStart' => '12.06.2022',
    'dataStop' => '15.06.2025',
    'sortBy' => 'data',
    'sortDirection' => 'desc',
    'page' => 1
];

echo "<div class='test-section info'>";
echo "<h2>🎯 Testing Specific Failing Parameters</h2>\n";
echo "<strong>Institution Category:</strong> curte_apel (Court of Appeal)<br>\n";
echo "<strong>Case Category:</strong> munca (Labor law)<br>\n";
echo "<strong>Date Period:</strong> 12.06.2022 - 15.06.2025<br>\n";
echo "<strong>Sorting:</strong> Data (descrescător) - Date (descending)<br>\n";
echo "<pre>" . print_r($testParams, true) . "</pre>\n";
echo "</div>";

try {
    // Test 1: Parameter Processing
    echo "<div class='test-section'>";
    echo "<h2>Test 1: Parameter Processing & Validation</h2>\n";
    
    $processedParams = [
        'numarDosar' => $testParams['numarDosar'] ?? '',
        'numeParte' => $testParams['numeParte'] ?? '',
        'obiectDosar' => $testParams['obiectDosar'] ?? '',
        'institutie' => $testParams['institutie'] ?? '',
        'categorieInstanta' => $testParams['categorieInstanta'] ?? '',
        'categorieCaz' => $testParams['categorieCaz'] ?? '',
        'dataStart' => $testParams['dataStart'] ?? '',
        'dataStop' => $testParams['dataStop'] ?? '',
        'dataUltimaModificareStart' => $testParams['dataUltimaModificareStart'] ?? '',
        'dataUltimaModificareStop' => $testParams['dataUltimaModificareStop'] ?? '',
        '_maxResults' => 500
    ];
    
    // Special handling for institution parameter
    if ($processedParams['institutie'] === '') {
        $processedParams['institutie'] = null;
    }
    
    echo "✅ Parameters processed successfully<br>\n";
    echo "<strong>Date Start:</strong> " . $processedParams['dataStart'] . "<br>\n";
    echo "<strong>Date Stop:</strong> " . $processedParams['dataStop'] . "<br>\n";
    echo "<strong>Institution Category:</strong> " . $processedParams['categorieInstanta'] . "<br>\n";
    echo "<strong>Case Category:</strong> " . $processedParams['categorieCaz'] . "<br>\n";
    
    // Validate date format
    $startTimestamp = strtotime($processedParams['dataStart']);
    $stopTimestamp = strtotime($processedParams['dataStop']);
    
    echo "<strong>Date validation:</strong> ";
    if ($startTimestamp && $stopTimestamp && $startTimestamp <= $stopTimestamp) {
        echo "✅ Valid date range<br>\n";
        echo "Start: " . date('Y-m-d', $startTimestamp) . " | Stop: " . date('Y-m-d', $stopTimestamp) . "<br>\n";
    } else {
        echo "❌ Invalid date range<br>\n";
    }
    echo "</div>";
    
    // Test 2: SOAP Search (without client-side filters)
    echo "<div class='test-section'>";
    echo "<h2>Test 2: SOAP API Search (Raw Results)</h2>\n";
    
    $dosarService = new \App\Services\DosarService();
    
    // Perform search with only SOAP-supported parameters
    $soapParams = [
        'numarDosar' => $processedParams['numarDosar'],
        'numeParte' => $processedParams['numeParte'],
        'obiectDosar' => $processedParams['obiectDosar'],
        'institutie' => $processedParams['institutie'],
        'dataStart' => $processedParams['dataStart'],
        'dataStop' => $processedParams['dataStop'],
        'dataUltimaModificareStart' => $processedParams['dataUltimaModificareStart'],
        'dataUltimaModificareStop' => $processedParams['dataUltimaModificareStop'],
        '_maxResults' => $processedParams['_maxResults']
    ];
    
    echo "Performing SOAP search with date range only...<br>\n";
    $searchStart = microtime(true);
    $rawResults = $dosarService->cautareAvansata($soapParams);
    $searchDuration = microtime(true) - $searchStart;
    
    echo "Search completed in " . round($searchDuration, 3) . " seconds<br>\n";
    echo "<strong>Raw SOAP results count:</strong> " . count($rawResults) . "<br>\n";
    
    if (!empty($rawResults)) {
        echo "✅ SOAP API returned results<br>\n";
        
        // Analyze first few results
        echo "<h4>Sample of raw results (first 3):</h4>\n";
        for ($i = 0; $i < min(3, count($rawResults)); $i++) {
            $result = $rawResults[$i];
            echo "<div class='result-item'>";
            echo "<strong>Result " . ($i + 1) . ":</strong><br>\n";
            echo "Număr: " . ($result->numar ?? 'N/A') . "<br>\n";
            echo "Instanță: " . ($result->institutie ?? 'N/A') . "<br>\n";
            echo "Obiect: " . ($result->obiect ?? 'N/A') . "<br>\n";
            echo "Data: " . ($result->data ?? 'N/A') . "<br>\n";
            echo "Stadiu: " . ($result->stadiuProcesual ?? 'N/A') . "<br>\n";
            echo "</div>";
        }
    } else {
        echo "⚠️ SOAP API returned no results<br>\n";
        echo "This indicates an issue with the basic search parameters or SOAP connectivity.<br>\n";
    }
    echo "</div>";
    
    // Test 3: Client-Side Filtering Analysis
    if (!empty($rawResults)) {
        echo "<div class='test-section'>";
        echo "<h2>Test 3: Client-Side Filtering Analysis</h2>\n";
        
        $categorieInstanta = $processedParams['categorieInstanta'];
        $categorieCaz = $processedParams['categorieCaz'];
        
        echo "<h4>Institution Category Filtering (categorieInstanta = '{$categorieInstanta}')</h4>\n";
        
        $institutionFiltered = [];
        foreach ($rawResults as $result) {
            $institutie = $result->institutie ?? '';
            $matchesInstitution = false;
            
            switch ($categorieInstanta) {
                case 'curte_apel':
                    $matchesInstitution = stripos($institutie, 'CurteadeApel') !== false || 
                                         stripos($institutie, 'Curtea de Apel') !== false;
                    break;
                case 'tribunal':
                    $matchesInstitution = stripos($institutie, 'Tribunalul') !== false;
                    break;
                case 'judecatorie':
                    $matchesInstitution = stripos($institutie, 'Judecatoria') !== false;
                    break;
                default:
                    $matchesInstitution = true;
            }
            
            if ($matchesInstitution) {
                $institutionFiltered[] = $result;
            }
        }
        
        echo "Results after institution filtering: <strong>" . count($institutionFiltered) . "</strong><br>\n";
        
        if (count($institutionFiltered) > 0) {
            echo "✅ Institution filtering working<br>\n";
            
            // Show sample institutions found
            $institutions = array_unique(array_map(function($r) { return $r->institutie ?? 'N/A'; }, array_slice($institutionFiltered, 0, 5)));
            echo "Sample institutions found: " . implode(', ', $institutions) . "<br>\n";
        } else {
            echo "❌ Institution filtering removed all results<br>\n";
            
            // Show what institutions we actually have
            $allInstitutions = array_unique(array_map(function($r) { return $r->institutie ?? 'N/A'; }, array_slice($rawResults, 0, 10)));
            echo "Available institutions in raw results: " . implode(', ', $allInstitutions) . "<br>\n";
        }
        
        echo "<h4>Case Category Filtering (categorieCaz = '{$categorieCaz}')</h4>\n";
        
        $finalFiltered = [];
        foreach ($institutionFiltered as $result) {
            $categorieCazDosar = strtolower($result->categorieCaz ?? '');
            $categorieCazNume = strtolower($result->categorieCazNume ?? '');
            $obiect = strtolower($result->obiect ?? '');
            
            $categorieCazLower = strtolower($categorieCaz);
            
            $matchesCategory = stripos($categorieCazDosar, $categorieCazLower) !== false ||
                              stripos($categorieCazNume, $categorieCazLower) !== false ||
                              stripos($obiect, $categorieCazLower) !== false;
            
            if ($matchesCategory) {
                $finalFiltered[] = $result;
            }
        }
        
        echo "Results after case category filtering: <strong>" . count($finalFiltered) . "</strong><br>\n";
        
        if (count($finalFiltered) > 0) {
            echo "✅ Case category filtering working<br>\n";
            
            // Show sample case objects found
            $objects = array_unique(array_map(function($r) { return $r->obiect ?? 'N/A'; }, array_slice($finalFiltered, 0, 3)));
            echo "Sample case objects found: " . implode(', ', $objects) . "<br>\n";
        } else {
            echo "❌ Case category filtering removed all results<br>\n";
            
            // Show what case categories/objects we actually have
            $allObjects = array_unique(array_map(function($r) { return $r->obiect ?? 'N/A'; }, array_slice($institutionFiltered, 0, 5)));
            echo "Available case objects in institution-filtered results: " . implode(', ', $allObjects) . "<br>\n";
        }
        
        echo "</div>";
        
        // Test 4: Sorting
        if (!empty($finalFiltered)) {
            echo "<div class='test-section'>";
            echo "<h2>Test 4: Sorting Analysis</h2>\n";
            
            $sortBy = $testParams['sortBy'] ?? 'data';
            $sortDirection = $testParams['sortDirection'] ?? 'desc';
            
            echo "Sorting by: <strong>{$sortBy}</strong> in <strong>{$sortDirection}</strong> order<br>\n";
            
            // Sort the results
            usort($finalFiltered, function($a, $b) use ($sortBy, $sortDirection) {
                $valueA = $a->$sortBy ?? '';
                $valueB = $b->$sortBy ?? '';
                
                if ($sortBy === 'data') {
                    $valueA = strtotime($valueA);
                    $valueB = strtotime($valueB);
                }
                
                $comparison = $valueA <=> $valueB;
                return $sortDirection === 'desc' ? -$comparison : $comparison;
            });
            
            echo "✅ Sorting applied<br>\n";
            echo "<strong>Final filtered and sorted results:</strong> " . count($finalFiltered) . "<br>\n";
            
            // Show first 3 sorted results
            echo "<h4>First 3 sorted results:</h4>\n";
            for ($i = 0; $i < min(3, count($finalFiltered)); $i++) {
                $result = $finalFiltered[$i];
                echo "<div class='result-item'>";
                echo "<strong>Result " . ($i + 1) . ":</strong><br>\n";
                echo "Număr: " . ($result->numar ?? 'N/A') . "<br>\n";
                echo "Instanță: " . ($result->institutie ?? 'N/A') . "<br>\n";
                echo "Obiect: " . ($result->obiect ?? 'N/A') . "<br>\n";
                echo "Data: " . ($result->data ?? 'N/A') . "<br>\n";
                echo "</div>";
            }
            
            echo "</div>";
        }
    }

    // Test 5: Test the exact search URL that's failing
    echo "<div class='test-section'>";
    echo "<h2>Test 5: Test Exact Failing URL</h2>\n";

    $testUrl = "http://localhost/just/search.php?categorieInstanta=curte_apel&categorieCaz=munca&dataStart=12.06.2022&dataStop=15.06.2025&sortBy=data&sortDirection=desc&page=1";
    echo "<p><strong>Testing URL:</strong> <a href='{$testUrl}' target='_blank'>{$testUrl}</a></p>\n";

    // Simulate the exact parameters that would be sent
    $urlParams = [
        'categorieInstanta' => 'curte_apel',
        'categorieCaz' => 'munca',
        'dataStart' => '12.06.2022',
        'dataStop' => '15.06.2025',
        'sortBy' => 'data',
        'sortDirection' => 'desc',
        'page' => '1'
    ];

    echo "<p>URL parameters:</p>\n";
    echo "<pre>" . print_r($urlParams, true) . "</pre>\n";

    // Test if these parameters would pass the hasSearchCriteria check
    $hasSearchCriteria = !empty($urlParams['numarDosar'] ?? '') ||
                        !empty($urlParams['numeParte'] ?? '') ||
                        !empty($urlParams['obiectDosar'] ?? '') ||
                        !empty($urlParams['institutie'] ?? '') ||
                        !empty($urlParams['categorieInstanta'] ?? '') ||
                        !empty($urlParams['categorieCaz'] ?? '') ||
                        !empty($urlParams['dataStart'] ?? '') ||
                        !empty($urlParams['dataStop'] ?? '');

    echo "<p><strong>Would pass hasSearchCriteria check:</strong> " . ($hasSearchCriteria ? '✅ Yes' : '❌ No') . "</p>\n";

    if ($hasSearchCriteria) {
        echo "<p>✅ The search should be executed with these parameters.</p>\n";
    } else {
        echo "<p>❌ The search would not be executed - this is the problem!</p>\n";
    }

    echo "</div>";

    // Test 6: Check if there are any issues with the search form itself
    echo "<div class='test-section'>";
    echo "<h2>Test 6: Search Form Validation</h2>\n";

    echo "<p>Testing if the search form would generate the correct parameters...</p>\n";

    // Simulate form processing like search.php does
    $formData = [
        'categorieInstanta' => 'curte_apel',
        'categorieCaz' => 'munca',
        'dataStart' => '12.06.2022',
        'dataStop' => '15.06.2025'
    ];

    $categorieInstanta = isset($formData['categorieInstanta']) ? trim($formData['categorieInstanta']) : '';
    $categorieCaz = isset($formData['categorieCaz']) ? trim($formData['categorieCaz']) : '';
    $dataStart = isset($formData['dataStart']) ? trim($formData['dataStart']) : '';
    $dataStop = isset($formData['dataStop']) ? trim($formData['dataStop']) : '';

    echo "<p><strong>Processed form data:</strong></p>\n";
    echo "<pre>";
    echo "categorieInstanta: '{$categorieInstanta}'\n";
    echo "categorieCaz: '{$categorieCaz}'\n";
    echo "dataStart: '{$dataStart}'\n";
    echo "dataStop: '{$dataStop}'\n";
    echo "</pre>";

    $formHasSearchCriteria = !empty($categorieInstanta) || !empty($categorieCaz) || !empty($dataStart) || !empty($dataStop);
    echo "<p><strong>Form has search criteria:</strong> " . ($formHasSearchCriteria ? '✅ Yes' : '❌ No') . "</p>\n";

    echo "</div>";

} catch (Exception $e) {
    echo "<div class='test-section error'>";
    echo "<h2>❌ Critical Error</h2>\n";
    echo "<strong>Error:</strong> " . htmlspecialchars($e->getMessage()) . "<br>\n";
    echo "<strong>File:</strong> " . $e->getFile() . "<br>\n";
    echo "<strong>Line:</strong> " . $e->getLine() . "<br>\n";
    echo "<h4>Stack trace:</h4>\n";
    echo "<pre>" . htmlspecialchars($e->getTraceAsString()) . "</pre>\n";
    echo "</div>";
}

echo "<div class='test-section info'>";
echo "<h2>🏁 Comprehensive Filtered Search Test Complete</h2>\n";
echo "<p>This test analyzes each step of the filtered search process to identify where the issue occurs.</p>";
echo "<p><strong>Key findings:</strong></p>";
echo "<ul>";
echo "<li>Check if SOAP API returns raw results with date range only</li>";
echo "<li>Verify client-side filtering for institution category (curte_apel)</li>";
echo "<li>Verify client-side filtering for case category (munca/labor law)</li>";
echo "<li>Check if search criteria validation is working correctly</li>";
echo "<li>Verify URL parameter processing matches form processing</li>";
echo "</ul>";
echo "</div>";
?>
