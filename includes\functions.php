<?php
/**
 * Funcții utilitare pentru aplicație
 */

/**
 * Formatează o dată în formatul specificat
 *
 * @param string $date Data în format string
 * @param string $format Formatul dorit (implicit DATE_FORMAT)
 * @return string Data formatată
 */
function formatDate($date, $format = DATE_FORMAT) {
    if (empty($date)) {
        return '';
    }

    $dateObj = new DateTime($date);
    return $dateObj->format($format);
}

/**
 * Curăță și validează datele de intrare
 *
 * @param string $data Datele de intrare
 * @return string Datele curățate
 */
function sanitizeInput($data) {
    $data = trim($data);
    $data = stripslashes($data);
    $data = htmlspecialchars($data);
    return $data;
}

/**
 * Generează un URL pentru paginare
 *
 * @param array $params Parametrii pentru URL
 * @param int $page Numărul paginii
 * @return string URL-ul generat
 */
function getPaginationUrl($params, $page) {
    $params['page'] = $page;
    return '?' . http_build_query($params);
}

/**
 * Generează HTML pentru paginare
 *
 * @param int $currentPage Pagina curentă
 * @param int $totalPages Numărul total de pagini
 * @param array $params Parametrii pentru URL
 * @return string HTML-ul pentru paginare
 */
function generatePagination($currentPage, $totalPages, $params) {
    if ($totalPages <= 1) {
        return '';
    }

    $html = '<div class="pagination">';

    // Buton pentru pagina anterioară
    if ($currentPage > 1) {
        $html .= '<a href="' . getPaginationUrl($params, $currentPage - 1) . '" class="page-link">&laquo; Anterior</a>';
    } else {
        $html .= '<span class="page-link disabled">&laquo; Anterior</span>';
    }

    // Paginile - afișăm maxim 9 pagini în jurul paginii curente pentru navigare îmbunătățită
    $startPage = max(1, $currentPage - 4);
    $endPage = min($totalPages, $currentPage + 4);

    if ($startPage > 1) {
        $html .= '<a href="' . getPaginationUrl($params, 1) . '" class="page-link">1</a>';
        if ($startPage > 2) {
            $html .= '<span class="page-link dots">...</span>';
        }
    }

    for ($i = $startPage; $i <= $endPage; $i++) {
        if ($i == $currentPage) {
            $html .= '<span class="page-link active">' . $i . '</span>';
        } else {
            $html .= '<a href="' . getPaginationUrl($params, $i) . '" class="page-link">' . $i . '</a>';
        }
    }

    if ($endPage < $totalPages) {
        if ($endPage < $totalPages - 1) {
            $html .= '<span class="page-link dots">...</span>';
        }
        $html .= '<a href="' . getPaginationUrl($params, $totalPages) . '" class="page-link">' . $totalPages . '</a>';
    }

    // Buton pentru pagina următoare
    if ($currentPage < $totalPages) {
        $html .= '<a href="' . getPaginationUrl($params, $currentPage + 1) . '" class="page-link">Următor &raquo;</a>';
    } else {
        $html .= '<span class="page-link disabled">Următor &raquo;</span>';
    }

    $html .= '</div>';

    return $html;
}

/**
 * Obține lista completă a instanțelor judecătorești din România
 * Lista este organizată alfabetic și include toate instanțele majore
 *
 * @return array Lista instanțelor
 */
function getInstanteList() {
    return [
        // Curtea Supremă
        'InaltaCurtedeCASSATIESIJUSTITIE' => 'Înalta Curte de Casație și Justiție',

        // Curți de Apel - ordine alfabetică
        'CurteadeApelALBAIULIA' => 'Curtea de Apel Alba Iulia',
        'CurteadeApelBACU' => 'Curtea de Apel Bacău',
        'CurteadeApelBRASSOV' => 'Curtea de Apel Brașov',
        'CurteadeApelBUCURESTI' => 'Curtea de Apel București',
        'CurteadeApelCLUJ' => 'Curtea de Apel Cluj',
        'CurteadeApelCONSTANTA' => 'Curtea de Apel Constanța',
        'CurteadeApelCRAIOVA' => 'Curtea de Apel Craiova',
        'CurteadeApelGALATI' => 'Curtea de Apel Galați',
        'CurteadeApelIASI' => 'Curtea de Apel Iași',
        'CurteadeApelORADEA' => 'Curtea de Apel Oradea',
        'CurteadeApelPITESTI' => 'Curtea de Apel Pitești',
        'CurteadeApelPLOIESTI' => 'Curtea de Apel Ploiești',
        'CurteadeApelSUCEAVA' => 'Curtea de Apel Suceava',
        'CurteadeApelTARGUMURES' => 'Curtea de Apel Târgu Mureș',
        'CurteadeApelTIMISOARA' => 'Curtea de Apel Timișoara',

        // Tribunale - ordine alfabetică
        'TribunalulALBA' => 'Tribunalul Alba',
        'TribunalulARAD' => 'Tribunalul Arad',
        'TribunalulARGES' => 'Tribunalul Argeș',
        'TribunalulBACU' => 'Tribunalul Bacău',
        'TribunalulBIHOR' => 'Tribunalul Bihor',
        'TribunalulBISTRITANASAUD' => 'Tribunalul Bistrița-Năsăud',
        'TribunalulBOTOSANI' => 'Tribunalul Botoșani',
        'TribunalulBRASSOV' => 'Tribunalul Brașov',
        'TribunalulBRASOV' => 'Tribunalul Brașov', // SOAP API variant without double S
        'TribunalulBRAILA' => 'Tribunalul Brăila',
        'TribunalulBUCURESTI' => 'Tribunalul București',
        'TribunalulBUZAU' => 'Tribunalul Buzău',
        'TribunalulCALARASI' => 'Tribunalul Călărași',
        'TribunalulCARASSSEVERIN' => 'Tribunalul Caraș-Severin',
        'TribunalulCLUJ' => 'Tribunalul Cluj',
        'TribunalulCONSTANTA' => 'Tribunalul Constanța',
        'TribunalulCOVASNA' => 'Tribunalul Covasna',
        'TribunalulDAMBOVITA' => 'Tribunalul Dâmbovița',
        'TribunalulDOLJ' => 'Tribunalul Dolj',
        'TribunalulGALATI' => 'Tribunalul Galați',
        'TribunalulGIURGIU' => 'Tribunalul Giurgiu',
        'TribunalulGORJ' => 'Tribunalul Gorj',
        'TribunalulHARGHITA' => 'Tribunalul Harghita',
        'TribunalulHUNEDOARA' => 'Tribunalul Hunedoara',
        'TribunalulIALOMITA' => 'Tribunalul Ialomița',
        'TribunalulIASI' => 'Tribunalul Iași',
        'TribunalulILFOV' => 'Tribunalul Ilfov',
        'TribunalulMARAMURES' => 'Tribunalul Maramureș',
        'TribunalulMEHEDINTI' => 'Tribunalul Mehedinți',
        'TribunalulMURES' => 'Tribunalul Mureș',
        'TribunalulNEAMT' => 'Tribunalul Neamț',
        'TribunalulOLT' => 'Tribunalul Olt',
        'TribunalulPRAHOVA' => 'Tribunalul Prahova',
        'TribunalulSALAJ' => 'Tribunalul Sălaj',
        'TribunalulSATUMARE' => 'Tribunalul Satu Mare',
        'TribunalulSIBIU' => 'Tribunalul Sibiu',
        'TribunalulSUCEAVA' => 'Tribunalul Suceava',
        'TribunalulTELEORMAN' => 'Tribunalul Teleorman',
        'TribunalulTIMIS' => 'Tribunalul Timiș',
        'TribunalulTULCEA' => 'Tribunalul Tulcea',
        'TribunalulVALCEA' => 'Tribunalul Vâlcea',
        'TribunalulVASLUI' => 'Tribunalul Vaslui',
        'TribunalulVRANCEA' => 'Tribunalul Vrancea',

        // Judecătorii București
        'JudecatoriaSECTORUL1BUCURESTI' => 'Judecătoria Sectorul 1 București',
        'JudecatoriaSECTORUL2BUCURESTI' => 'Judecătoria Sectorul 2 București',
        'JudecatoriaSECTORUL3BUCURESTI' => 'Judecătoria Sectorul 3 București',
        'JudecatoriaSECTORUL4BUCURESTI' => 'Judecătoria Sectorul 4 București',
        'JudecatoriaSECTORUL5BUCURESTI' => 'Judecătoria Sectorul 5 București',
        'JudecatoriaSECTORUL6BUCURESTI' => 'Judecătoria Sectorul 6 București',

        // Judecătorii principale - ordine alfabetică
        'JudecatoriaAIUD' => 'Judecătoria Aiud',
        'JudecatoriaALBAIULIA' => 'Judecătoria Alba Iulia',
        'JudecatoriaALEXANDRIA' => 'Judecătoria Alexandria',
        'JudecatoriaARAD' => 'Judecătoria Arad',
        'JudecatoriaBACU' => 'Judecătoria Bacău',
        'JudecatoriaBISTRITA' => 'Judecătoria Bistrița',
        'JudecatoriaBOTOSANI' => 'Judecătoria Botoșani',
        'JudecatoriaBRASSOV' => 'Judecătoria Brașov',
        'JudecatoriaBRAILA' => 'Judecătoria Brăila',
        'JudecatoriaBUZAU' => 'Judecătoria Buzău',
        'JudecatoriaBUFTEA' => 'Judecătoria Buftea',
        'JudecatoriaCALARASI' => 'Judecătoria Călărași',
        'JudecatoriaCARACAL' => 'Judecătoria Caracal',
        'JudecatoriaCLUJNAPOCA' => 'Judecătoria Cluj-Napoca',
        'JudecatoriaCONSTANTA' => 'Judecătoria Constanța',
        'JudecatoriaCRAIOVA' => 'Judecătoria Craiova',
        'JudecatoriaDEVA' => 'Judecătoria Deva',
        'JudecatoriaFOCSANI' => 'Judecătoria Focșani',
        'JudecatoriaGALATI' => 'Judecătoria Galați',
        'JudecatoriaGIURGIU' => 'Judecătoria Giurgiu',
        'JudecatoriaIASI' => 'Judecătoria Iași',
        'JudecatoriaORADEA' => 'Judecătoria Oradea',
        'JudecatoriaPITESTI' => 'Judecătoria Pitești',
        'JudecatoriaPLOIESTI' => 'Judecătoria Ploiești',
        'JudecatoriaREMNICUVILCEA' => 'Judecătoria Râmnicu Vâlcea',
        'JudecatoriaSATUMARE' => 'Judecătoria Satu Mare',
        'JudecatoriaSIBIU' => 'Judecătoria Sibiu',
        'JudecatoriaSLATINA' => 'Judecătoria Slatina',
        'JudecatoriaSUCEAVA' => 'Judecătoria Suceava',
        'JudecatoriaTARGOVISTE' => 'Judecătoria Târgoviște',
        'JudecatoriaTARGUJIU' => 'Judecătoria Târgu Jiu',
        'JudecatoriaTARGUMURES' => 'Judecătoria Târgu Mureș',
        'JudecatoriaTIMISOARA' => 'Judecătoria Timișoara',
        'JudecatoriaTULCEA' => 'Judecătoria Tulcea',
        'JudecatoriaVASLUI' => 'Judecătoria Vaslui',
        'JudecatoriaZALAU' => 'Judecătoria Zalău',

        // Additional institution codes that might appear in SOAP API responses
        // These are common variations and additional courts
        'TribunalulComercialBUCURESTI' => 'Tribunalul Comercial București',
        'TribunalulComercialCLUJ' => 'Tribunalul Comercial Cluj',
        'TribunalulComercialCONSTANTA' => 'Tribunalul Comercial Constanța',
        'TribunalulComercialTIMISOARA' => 'Tribunalul Comercial Timișoara',

        // Military courts
        'TribunalulMilitarBUCURESTI' => 'Tribunalul Militar București',
        'TribunalulMilitarCLUJ' => 'Tribunalul Militar Cluj',

        // Specialized courts
        'TribunalulPentruMinoriSiFamilieBUCURESTI' => 'Tribunalul pentru Minori și Familie București',
        'TribunalulPentruMinoriSiFamilieCLUJ' => 'Tribunalul pentru Minori și Familie Cluj',

        // Additional regional variations that might appear
        'CurteadeApelBUCURESTISECTIACOMERCIALA' => 'Curtea de Apel București - Secția Comercială',
        'CurteadeApelBUCURESTISECTIAPENALA' => 'Curtea de Apel București - Secția Penală',
        'CurteadeApelBUCURESTISECTIACIVILA' => 'Curtea de Apel București - Secția Civilă',

        // Additional common institution codes that might be missing
        'TribunalulBUCURESTI' => 'Tribunalul București', // Ensure this common variant is covered
        'JudecatoriaBUCURESTI' => 'Judecătoria București', // General Bucharest court
        'TribunalulMUNICIPIULUIBUCURESTI' => 'Tribunalul Municipiului București',

        // Additional Judecătorii that might appear
        'JudecatoriaCAMPINA' => 'Judecătoria Câmpina',
        'JudecatoriaCOMPIEGNE' => 'Judecătoria Compiègne',
        'JudecatoriaDROBETATURNUSEVERIN' => 'Judecătoria Drobeta-Turnu Severin',
        'JudecatoriaHUNEDOARA' => 'Judecătoria Hunedoara',
        'JudecatoriaMANGALIA' => 'Judecătoria Mangalia',
        'JudecatoriaMEDGIDIA' => 'Judecătoria Medgidia',
        'JudecatoriaNAVODARI' => 'Judecătoria Năvodari',
        'JudecatoriaOTOPENI' => 'Judecătoria Otopeni',
        'JudecatoriaPANTELIMON' => 'Judecătoria Pantelimon',
        'JudecatoriaRAMNICUVALCEA' => 'Judecătoria Râmnicu Vâlcea',
        'JudecatoriaTURDA' => 'Judecătoria Turda',
        'JudecatoriaVOLUNTARI' => 'Judecătoria Voluntari',
    ];
}

/**
 * Alias pentru getInstanteList() pentru compatibilitate cu codul existent
 *
 * @return array Lista instanțelor
 */
function getInstituteList() {
    return getInstanteList();
}
