/**
 * Stiluri pentru funcționalitatea modernă de export PDF
 * Design minimalist și funcțional
 */

/* Stiluri pentru butonul de export PDF */
.modern-pdf-btn {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    padding: 0.5rem 1rem;
    transition: all 0.3s ease;
    background-color: #007bff;
    color: white;
    border: none;
    border-radius: 4px;
    font-weight: 500;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    min-height: 44px;
    position: relative;
    overflow: hidden;
}

.modern-pdf-btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(rgba(255, 255, 255, 0.1), rgba(255, 255, 255, 0));
    opacity: 0;
    transition: opacity 0.3s ease;
}

.modern-pdf-btn:hover {
    background-color: #0069d9;
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
}

.modern-pdf-btn:hover::before {
    opacity: 1;
}

.modern-pdf-btn:active {
    transform: translateY(0);
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

.modern-pdf-btn i {
    margin-right: 8px;
    font-size: 1.1em;
}

/* Stiluri pentru indicatorul de încărcare */
.pdf-loading-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.7);
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    z-index: 9999;
    color: white;
    text-align: center;
    opacity: 0;
    transition: opacity 0.3s ease;
    backdrop-filter: blur(3px);
}

.pdf-loading-overlay.show {
    opacity: 1;
}

.pdf-loading-spinner {
    width: 60px;
    height: 60px;
    border: 4px solid rgba(255, 255, 255, 0.3);
    border-radius: 50%;
    border-top-color: white;
    animation: pdf-spin 1s linear infinite;
}

@keyframes pdf-spin {
    to { transform: rotate(360deg); }
}

.pdf-loading-text {
    margin-top: 20px;
    font-size: 18px;
    font-weight: 500;
}

.pdf-loading-subtext {
    margin-top: 10px;
    font-size: 14px;
    opacity: 0.8;
    max-width: 80%;
}

/* Stiluri pentru optimizarea PDF-ului */
.pdf-optimized {
    font-family: 'Roboto', 'Open Sans', Arial, sans-serif;
    line-height: 1.5;
}

.pdf-optimized h1, 
.pdf-optimized h2, 
.pdf-optimized h3, 
.pdf-optimized h4, 
.pdf-optimized h5, 
.pdf-optimized h6 {
    margin-top: 1em;
    margin-bottom: 0.5em;
    page-break-after: avoid;
}

.pdf-optimized p {
    margin-bottom: 0.75em;
}

.pdf-optimized table {
    width: 100%;
    border-collapse: collapse;
    margin-bottom: 1em;
    page-break-inside: avoid;
}

.pdf-optimized table th,
.pdf-optimized table td {
    padding: 8px;
    border: 1px solid #ddd;
}

.pdf-optimized table th {
    background-color: #f5f5f5;
    font-weight: bold;
    text-align: left;
}

.pdf-optimized img {
    max-width: 100%;
    height: auto;
}

/* Stiluri responsive */
@media (max-width: 768px) {
    .modern-pdf-btn {
        width: 100%;
        margin-bottom: 0.5rem;
    }
    
    .pdf-loading-text {
        font-size: 16px;
    }
    
    .pdf-loading-subtext {
        font-size: 12px;
        max-width: 90%;
    }
}
