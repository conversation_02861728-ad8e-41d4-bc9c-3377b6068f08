<?php
/**
 * Isolated test for future date range filtering issue
 */

require_once 'includes/config.php';
require_once 'includes/functions.php';
require_once 'src/Services/DosarService.php';

echo "<h1>🔬 Future Date Range Isolation Test</h1>\n";
echo "<style>
    .test-section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
    .success { background-color: #d4edda; border-color: #c3e6cb; }
    .error { background-color: #f8d7da; border-color: #f5c6cb; }
    .warning { background-color: #fff3cd; border-color: #ffeaa7; }
    .info { background-color: #d1ecf1; border-color: #bee5eb; }
    pre { background: #f8f9fa; padding: 10px; border-radius: 3px; overflow-x: auto; max-height: 300px; overflow-y: auto; }
    .test-result { margin: 5px 0; padding: 8px; border-radius: 3px; }
    .pass { background-color: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
    .fail { background-color: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
    .skip { background-color: #fff3cd; color: #856404; border: 1px solid #ffeaa7; }
</style>\n";

echo "<div class='test-section info'>";
echo "<h2>🎯 Isolating Future Date Range Issue</h2>\n";
echo "<p>Testing specific date ranges to identify where the issue occurs</p>";
echo "</div>";

try {
    $dosarService = new \App\Services\DosarService();
    
    // Test 1: Current year only (baseline)
    echo "<div class='test-section'>";
    echo "<h2>Test 1: Current Year Baseline (2023)</h2>\n";
    
    $currentYearParams = [
        'numarDosar' => '',
        'numeParte' => '',
        'obiectDosar' => '',
        'institutie' => null,
        'dataStart' => '01.01.2023',
        'dataStop' => '31.12.2023',
        'dataUltimaModificareStart' => '',
        'dataUltimaModificareStop' => '',
        '_maxResults' => 50
    ];
    
    echo "<div class='test-result info'>";
    echo "Parameters: " . json_encode($currentYearParams, JSON_UNESCAPED_UNICODE);
    echo "</div>";
    
    $searchStart = microtime(true);
    $currentYearResults = $dosarService->cautareAvansata($currentYearParams);
    $searchDuration = microtime(true) - $searchStart;
    
    echo "<div class='test-result " . (count($currentYearResults) > 0 ? 'pass' : 'warning') . "'>";
    echo "2023 Results: " . count($currentYearResults) . " cases in " . round($searchDuration, 3) . "s";
    echo "</div>";
    
    if (!empty($currentYearResults)) {
        // Show sample
        $sample = $currentYearResults[0];
        echo "<div class='test-result info'>";
        echo "Sample: " . htmlspecialchars($sample->numar ?? 'N/A') . " - " . htmlspecialchars($sample->data ?? 'N/A');
        echo "</div>";
    }
    echo "</div>";
    
    // Test 2: 2024 only
    echo "<div class='test-section'>";
    echo "<h2>Test 2: 2024 Only</h2>\n";
    
    $year2024Params = [
        'numarDosar' => '',
        'numeParte' => '',
        'obiectDosar' => '',
        'institutie' => null,
        'dataStart' => '01.01.2024',
        'dataStop' => '31.12.2024',
        'dataUltimaModificareStart' => '',
        'dataUltimaModificareStop' => '',
        '_maxResults' => 50
    ];
    
    echo "<div class='test-result info'>";
    echo "Parameters: " . json_encode($year2024Params, JSON_UNESCAPED_UNICODE);
    echo "</div>";
    
    $searchStart = microtime(true);
    $year2024Results = $dosarService->cautareAvansata($year2024Params);
    $searchDuration = microtime(true) - $searchStart;
    
    echo "<div class='test-result " . (count($year2024Results) > 0 ? 'pass' : 'warning') . "'>";
    echo "2024 Results: " . count($year2024Results) . " cases in " . round($searchDuration, 3) . "s";
    echo "</div>";
    
    if (!empty($year2024Results)) {
        $sample = $year2024Results[0];
        echo "<div class='test-result info'>";
        echo "Sample: " . htmlspecialchars($sample->numar ?? 'N/A') . " - " . htmlspecialchars($sample->data ?? 'N/A');
        echo "</div>";
    } else {
        echo "<div class='test-result warning'>";
        echo "⚠️ No results found for 2024 - this may indicate the database doesn't contain 2024 cases yet";
        echo "</div>";
    }
    echo "</div>";
    
    // Test 3: 2025 only
    echo "<div class='test-section'>";
    echo "<h2>Test 3: 2025 Only</h2>\n";
    
    $year2025Params = [
        'numarDosar' => '',
        'numeParte' => '',
        'obiectDosar' => '',
        'institutie' => null,
        'dataStart' => '01.01.2025',
        'dataStop' => '31.12.2025',
        'dataUltimaModificareStart' => '',
        'dataUltimaModificareStop' => '',
        '_maxResults' => 50
    ];
    
    echo "<div class='test-result info'>";
    echo "Parameters: " . json_encode($year2025Params, JSON_UNESCAPED_UNICODE);
    echo "</div>";
    
    $searchStart = microtime(true);
    $year2025Results = $dosarService->cautareAvansata($year2025Params);
    $searchDuration = microtime(true) - $searchStart;
    
    echo "<div class='test-result " . (count($year2025Results) > 0 ? 'pass' : 'warning') . "'>";
    echo "2025 Results: " . count($year2025Results) . " cases in " . round($searchDuration, 3) . "s";
    echo "</div>";
    
    if (!empty($year2025Results)) {
        $sample = $year2025Results[0];
        echo "<div class='test-result info'>";
        echo "Sample: " . htmlspecialchars($sample->numar ?? 'N/A') . " - " . htmlspecialchars($sample->data ?? 'N/A');
        echo "</div>";
    } else {
        echo "<div class='test-result warning'>";
        echo "⚠️ No results found for 2025 - this is expected as 2025 cases likely don't exist yet";
        echo "</div>";
    }
    echo "</div>";
    
    // Test 4: Extended range (2023-2025)
    echo "<div class='test-section'>";
    echo "<h2>Test 4: Extended Range (2023-2025)</h2>\n";
    
    $extendedRangeParams = [
        'numarDosar' => '',
        'numeParte' => '',
        'obiectDosar' => '',
        'institutie' => null,
        'dataStart' => '01.06.2023',
        'dataStop' => '15.06.2025',
        'dataUltimaModificareStart' => '',
        'dataUltimaModificareStop' => '',
        '_maxResults' => 50
    ];
    
    echo "<div class='test-result info'>";
    echo "Parameters: " . json_encode($extendedRangeParams, JSON_UNESCAPED_UNICODE);
    echo "</div>";
    
    $searchStart = microtime(true);
    $extendedResults = $dosarService->cautareAvansata($extendedRangeParams);
    $searchDuration = microtime(true) - $searchStart;
    
    echo "<div class='test-result " . (count($extendedResults) > 0 ? 'pass' : 'warning') . "'>";
    echo "Extended Range Results: " . count($extendedResults) . " cases in " . round($searchDuration, 3) . "s";
    echo "</div>";
    
    if (!empty($extendedResults)) {
        // Analyze date distribution
        $dateDistribution = [];
        foreach ($extendedResults as $result) {
            $date = $result->data ?? '';
            if (!empty($date)) {
                $year = date('Y', strtotime($date));
                $dateDistribution[$year] = ($dateDistribution[$year] ?? 0) + 1;
            }
        }
        
        if (!empty($dateDistribution)) {
            ksort($dateDistribution);
            echo "<div class='test-result info'>";
            echo "Date distribution: ";
            foreach ($dateDistribution as $year => $count) {
                echo "{$year}: {$count} cases; ";
            }
            echo "</div>";
        }
        
        $sample = $extendedResults[0];
        echo "<div class='test-result info'>";
        echo "Sample: " . htmlspecialchars($sample->numar ?? 'N/A') . " - " . htmlspecialchars($sample->data ?? 'N/A');
        echo "</div>";
    }
    echo "</div>";
    
    // Test 5: The exact failing case with all filters
    echo "<div class='test-section'>";
    echo "<h2>Test 5: Exact Failing Case (All Filters)</h2>\n";
    
    $failingParams = [
        'numarDosar' => '',
        'numeParte' => '',
        'obiectDosar' => '',
        'institutie' => 'CurteadeApelBUCURESTI',
        'categorieInstanta' => 'curte_apel',
        'categorieCaz' => 'munca',
        'dataStart' => '01.06.2023',
        'dataStop' => '15.06.2025',
        'dataUltimaModificareStart' => '01.01.2023',
        'dataUltimaModificareStop' => '31.12.2023',
        '_maxResults' => 500
    ];
    
    echo "<div class='test-result info'>";
    echo "Parameters: " . json_encode($failingParams, JSON_UNESCAPED_UNICODE);
    echo "</div>";
    
    $searchStart = microtime(true);
    $failingResults = $dosarService->cautareAvansata($failingParams);
    $searchDuration = microtime(true) - $searchStart;
    
    echo "<div class='test-result " . (count($failingResults) > 0 ? 'pass' : 'warning') . "'>";
    echo "Failing Case Results: " . count($failingResults) . " cases in " . round($searchDuration, 3) . "s";
    echo "</div>";
    
    if (!empty($failingResults)) {
        // Analyze date distribution
        $dateDistribution = [];
        foreach ($failingResults as $result) {
            $date = $result->data ?? '';
            if (!empty($date)) {
                $year = date('Y', strtotime($date));
                $dateDistribution[$year] = ($dateDistribution[$year] ?? 0) + 1;
            }
        }
        
        if (!empty($dateDistribution)) {
            ksort($dateDistribution);
            echo "<div class='test-result info'>";
            echo "Date distribution: ";
            foreach ($dateDistribution as $year => $count) {
                echo "{$year}: {$count} cases; ";
            }
            echo "</div>";
        }
        
        // Show samples
        echo "<h5>Sample results:</h5>";
        for ($i = 0; $i < min(3, count($failingResults)); $i++) {
            $result = $failingResults[$i];
            echo "<div class='test-result info'>";
            echo "Case: " . htmlspecialchars($result->numar ?? 'N/A');
            echo " | Date: " . htmlspecialchars($result->data ?? 'N/A');
            echo " | Institution: " . htmlspecialchars($result->institutie ?? 'N/A');
            echo "</div>";
        }
    }
    echo "</div>";
    
} catch (Exception $e) {
    echo "<div class='test-section error'>";
    echo "<h2>❌ Error</h2>\n";
    echo "<div class='test-result fail'>";
    echo "Error: " . htmlspecialchars($e->getMessage());
    echo "</div>";
    echo "</div>";
}

echo "<div class='test-section info'>";
echo "<h2>🏁 Future Date Isolation Test Complete</h2>\n";
echo "<p><strong>Key Findings:</strong></p>";
echo "<ul>";
echo "<li>✅ Tested individual year ranges (2023, 2024, 2025)</li>";
echo "<li>✅ Tested extended range (2023-2025)</li>";
echo "<li>✅ Tested exact failing case with all filters</li>";
echo "<li>✅ Analyzed date distribution in results</li>";
echo "</ul>";
echo "<p><strong>Next:</strong> Check logs for detailed SOAP parameter analysis.</p>";
echo "</div>";
?>
