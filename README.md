# Portal Judiciar

Aplicație web pentru căutarea și vizualizarea dosarelor judecătorești din România.

## Cerințe

- PHP 7.4 sau mai nou
- Extensia SOAP pentru PHP
- Extensia mbstring pentru PHP
- Extensia intl pentru PHP (opțional, pentru gestionarea avansată a diacriticelor)
- Composer pentru gestionarea dependințelor

## Instalare

1. Clonați repository-ul:
   ```
   git clone https://github.com/your-username/portal-judiciar.git
   cd portal-judiciar
   ```

2. Instalați dependințele cu Composer:
   ```
   composer install
   ```

3. Configurați un server web (Apache, Nginx) pentru a servi aplicația din directorul public/

4. Asigurați-vă că directoarele logs/, cache/ și temp/ au permisiuni de scriere:
   ```
   chmod -R 755 logs/ cache/ temp/
   ```

## Structura proiectului

```
portal-judiciar/
├── bootstrap.php           # Fișier de inițializare a aplicației
├── composer.json           # Configurare Composer
├── public/                 # Directorul public accesibil prin web
│   ├── index.php           # Punctul de intrare în aplicație
│   ├── assets/             # Resurse statice (CSS, JS, imagini)
│   └── .htaccess           # Configurare Apache
├── src/                    # Codul sursă al aplicației
│   ├── Config/             # Fișiere de configurare
│   ├── Controllers/        # Controllere
│   ├── Helpers/            # Funcții helper
│   ├── Models/             # Modele de date
│   ├── Services/           # Servicii pentru logica de business
│   └── Templates/          # Șabloane Twig
├── tests/                  # Teste
│   ├── Unit/               # Teste unitare
│   ├── Integration/        # Teste de integrare
│   └── Functional/         # Teste funcționale
├── logs/                   # Loguri
├── cache/                  # Fișiere cache
└── temp/                   # Fișiere temporare
```

## Funcționalități

- Căutare dosare după număr
- Căutare dosare după nume parte
- Căutare dosare după obiect
- Căutare avansată cu filtre multiple
- Vizualizare detalii dosar
- Export date în format PDF
- Trimitere informații prin email

## Dezvoltare

### Rularea testelor

```
composer test
```

### Verificarea stilului de codare

```
composer check-style
```

### Corectarea automată a stilului de codare

```
composer fix-style
```

## Contribuții

Contribuțiile sunt binevenite! Vă rugăm să urmați acești pași:

1. Fork repository-ul
2. Creați un branch pentru funcționalitatea nouă (`git checkout -b feature/amazing-feature`)
3. Faceți commit la modificări (`git commit -m 'Add some amazing feature'`)
4. Push la branch (`git push origin feature/amazing-feature`)
5. Deschideți un Pull Request

## Licență

Acest proiect este licențiat sub [MIT License](LICENSE).
