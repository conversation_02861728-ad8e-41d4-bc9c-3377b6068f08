<?php
/**
 * Comprehensive audit of case category filtering functionality
 */

require_once 'includes/config.php';
require_once 'includes/functions.php';
require_once 'src/Services/DosarService.php';

echo "<h1>📋 Case Category Filtering - Comprehensive Audit</h1>\n";
echo "<style>
    .audit-section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
    .success { background-color: #d4edda; border-color: #c3e6cb; }
    .error { background-color: #f8d7da; border-color: #f5c6cb; }
    .warning { background-color: #fff3cd; border-color: #ffeaa7; }
    .info { background-color: #d1ecf1; border-color: #bee5eb; }
    pre { background: #f8f9fa; padding: 10px; border-radius: 3px; overflow-x: auto; max-height: 400px; overflow-y: auto; }
    .test-result { margin: 5px 0; padding: 8px; border-radius: 3px; }
    .pass { background-color: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
    .fail { background-color: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
    .skip { background-color: #fff3cd; color: #856404; border: 1px solid #ffeaa7; }
    .enhanced { background-color: #e7f3ff; border: 1px solid #b3d9ff; color: #0056b3; }
    .basic { background-color: #f8f9fa; border: 1px solid #dee2e6; color: #495057; }
    .missing { background-color: #fff3cd; border: 1px solid #ffeaa7; color: #856404; }
    .category-table { width: 100%; border-collapse: collapse; margin: 10px 0; }
    .category-table th, .category-table td { border: 1px solid #ddd; padding: 8px; text-align: left; }
    .category-table th { background-color: #f8f9fa; font-weight: bold; }
</style>\n";

echo "<div class='audit-section info'>";
echo "<h2>🎯 Case Category Filtering Audit Scope</h2>\n";
echo "<p><strong>Objectives:</strong></p>";
echo "<ul>";
echo "<li>✅ Inventory all case category options from the advanced search form</li>";
echo "<li>✅ Review current client-side filtering implementation</li>";
echo "<li>✅ Test enhanced terminology matching for each category</li>";
echo "<li>✅ Validate Romanian legal terminology and diacritics handling</li>";
echo "<li>✅ Identify categories needing enhanced terminology matching</li>";
echo "</ul>";
echo "</div>";

// Step 1: Extract case category options from the form
echo "<div class='audit-section'>";
echo "<h2>Step 1: Case Category Options Inventory</h2>\n";

echo "<h4>1.1 Extracting Options from Advanced Search Form</h4>\n";

// Read the index.php file to extract case category options
$indexContent = file_get_contents('index.php');

// Extract the case category select options using regex
preg_match('/<select[^>]*name=["\']categorieCaz["\'][^>]*>(.*?)<\/select>/s', $indexContent, $matches);

if (!empty($matches[1])) {
    $selectContent = $matches[1];
    
    // Extract all option values and labels
    preg_match_all('/<option[^>]*value=["\']([^"\']*)["\'][^>]*>(.*?)<\/option>/s', $selectContent, $optionMatches, PREG_SET_ORDER);
    
    $caseCategories = [];
    foreach ($optionMatches as $match) {
        $value = trim($match[1]);
        $label = trim(strip_tags($match[2]));
        
        if (!empty($value)) {
            $caseCategories[$value] = $label;
        }
    }
    
    echo "<div class='test-result pass'>";
    echo "✅ Successfully extracted " . count($caseCategories) . " case category options from the form";
    echo "</div>";
    
    echo "<table class='category-table'>";
    echo "<tr><th>Value</th><th>Label (Romanian)</th><th>Translation/Description</th></tr>";
    
    // Define translations/descriptions for better understanding
    $translations = [
        'civil' => 'Civil Law',
        'penal' => 'Criminal Law',
        'comercial' => 'Commercial Law',
        'munca' => 'Labor Law',
        'contencios_administrativ' => 'Administrative Litigation',
        'fiscal' => 'Tax Law',
        'familie' => 'Family Law',
        'succesiuni' => 'Inheritance Law',
        'proprietate_intelectuala' => 'Intellectual Property',
        'asigurari' => 'Insurance Law',
        'bancar' => 'Banking Law',
        'concurenta' => 'Competition Law',
        'consumatori' => 'Consumer Protection',
        'mediu' => 'Environmental Law',
        'energie' => 'Energy Law',
        'transport' => 'Transportation Law',
        'telecomunicatii' => 'Telecommunications',
        'constructii' => 'Construction Law',
        'urbanism' => 'Urban Planning',
        'sanatate' => 'Health Law',
        'educatie' => 'Education Law',
        'sport' => 'Sports Law',
        'turism' => 'Tourism Law'
    ];
    
    foreach ($caseCategories as $value => $label) {
        $translation = $translations[$value] ?? 'Unknown category';
        echo "<tr>";
        echo "<td><code>{$value}</code></td>";
        echo "<td>{$label}</td>";
        echo "<td><em>{$translation}</em></td>";
        echo "</tr>";
    }
    echo "</table>";
    
} else {
    echo "<div class='test-result fail'>";
    echo "❌ Failed to extract case category options from the form";
    echo "</div>";
    $caseCategories = [];
}

echo "</div>";

// Step 2: Analyze current implementation
echo "<div class='audit-section'>";
echo "<h2>Step 2: Current Implementation Analysis</h2>\n";

echo "<h4>2.1 Reviewing Client-Side Filtering Logic</h4>\n";

// Read the DosarService.php file to analyze current implementation
$dosarServiceContent = file_get_contents('src/Services/DosarService.php');

// Extract the case category filtering logic
preg_match('/\/\/ Pentru "munca".*?return \$matches;/s', $dosarServiceContent, $filteringLogic);

if (!empty($filteringLogic[0])) {
    echo "<div class='test-result pass'>";
    echo "✅ Successfully located case category filtering logic in DosarService.php";
    echo "</div>";
    
    // Analyze which categories have enhanced logic
    $enhancedCategories = [];
    $basicCategories = [];
    
    // Check for enhanced logic patterns
    if (strpos($dosarServiceContent, 'munca') !== false) {
        $enhancedCategories['munca'] = 'Labor Law - Enhanced terminology matching';
    }
    
    if (strpos($dosarServiceContent, 'contencios_administrativ') !== false) {
        $enhancedCategories['contencios_administrativ'] = 'Administrative Litigation - Enhanced terminology matching';
    }
    
    // All other categories use basic string matching
    foreach ($caseCategories as $value => $label) {
        if (!isset($enhancedCategories[$value])) {
            $basicCategories[$value] = $label . ' - Basic string matching';
        }
    }
    
    echo "<h5>Enhanced Terminology Categories:</h5>";
    if (!empty($enhancedCategories)) {
        foreach ($enhancedCategories as $value => $description) {
            echo "<div class='test-result enhanced'>";
            echo "🔧 <strong>{$value}</strong>: {$description}";
            echo "</div>";
        }
    } else {
        echo "<div class='test-result warning'>";
        echo "⚠️ No enhanced terminology categories found";
        echo "</div>";
    }
    
    echo "<h5>Basic String Matching Categories:</h5>";
    if (!empty($basicCategories)) {
        $count = 0;
        foreach ($basicCategories as $value => $description) {
            if ($count < 5) { // Show first 5, then summarize
                echo "<div class='test-result basic'>";
                echo "📝 <strong>{$value}</strong>: {$description}";
                echo "</div>";
            }
            $count++;
        }
        
        if ($count > 5) {
            echo "<div class='test-result basic'>";
            echo "📝 ... and " . ($count - 5) . " more categories using basic string matching";
            echo "</div>";
        }
    }
    
} else {
    echo "<div class='test-result fail'>";
    echo "❌ Failed to locate case category filtering logic";
    echo "</div>";
}

echo "</div>";

echo "<div class='audit-section info'>";
echo "<h2>🏁 Case Category Audit Phase 1 Complete</h2>\n";
echo "<p>This covers inventory and implementation analysis.</p>";
echo "<p><strong>Next:</strong> Enhanced terminology testing and pattern matching validation.</p>";
echo "</div>";

// Step 3: Enhanced Terminology Testing
echo "<div class='audit-section'>";
echo "<h2>Step 3: Enhanced Terminology Testing</h2>\n";

echo "<h4>3.1 Testing Current Enhanced Categories</h4>\n";

// Test the enhanced categories we know exist
$enhancedTests = [
    'munca' => [
        'test_terms' => ['munca', 'muncă', 'salarial', 'angajat', 'contract de munca', 'concediere'],
        'expected_matches' => ['munca', 'muncă', 'salarial', 'angajat', 'contract de munca', 'concediere'],
        'non_matches' => ['civil', 'penal', 'comercial']
    ],
    'contencios_administrativ' => [
        'test_terms' => ['contencios administrativ', 'administrativ', 'act administrativ', 'autoritate publica', 'contencios fiscal'],
        'expected_matches' => ['contencios administrativ', 'administrativ', 'act administrativ', 'autoritate publica', 'contencios fiscal'],
        'non_matches' => ['civil', 'penal', 'comercial']
    ]
];

foreach ($enhancedTests as $category => $tests) {
    echo "<h5>Testing Enhanced Category: {$category}</h5>";

    foreach ($tests['test_terms'] as $term) {
        // Simulate the enhanced matching logic
        $matches = false;

        if ($category === 'munca') {
            $laborTerms = ['munca', 'muncă', 'salarial', 'salariat', 'angajat', 'angajator',
                          'contract de munca', 'contract de muncă', 'individual de munca',
                          'individual de muncă', 'concediere', 'licențiere', 'despăgubiri',
                          'daune morale', 'discriminare', 'hărțuire', 'overtime', 'ore suplimentare'];

            foreach ($laborTerms as $laborTerm) {
                if (stripos($term, $laborTerm) !== false) {
                    $matches = true;
                    break;
                }
            }
        } elseif ($category === 'contencios_administrativ') {
            $adminTerms = ['contencios administrativ', 'contencios', 'administrativ', 'administrative',
                          'litigiu administrativ', 'drept administrativ', 'admin', 'contenciosul administrativ',
                          'act administrativ', 'decizie administrativa', 'decizie administrativă',
                          'autoritate publica', 'autoritate publică', 'instituție publică', 'institutie publica',
                          'anulare act', 'anularea actului', 'obligarea la', 'constatarea nulității',
                          'repararea prejudiciului', 'daune-interese', 'contencios fiscal',
                          'contencios urbanistic', 'contencios în materie', 'recurs administrativ'];

            foreach ($adminTerms as $adminTerm) {
                if (stripos($term, $adminTerm) !== false) {
                    $matches = true;
                    break;
                }
            }
        }

        $expected = in_array($term, $tests['expected_matches']);
        $testPassed = ($matches === $expected);

        echo "<div class='test-result " . ($testPassed ? 'pass' : 'fail') . "'>";
        echo "'{$term}' → " . ($matches ? 'MATCH' : 'NO MATCH') . " " . ($testPassed ? '✅' : '❌');
        echo "</div>";
    }
}

echo "</div>";

// Step 4: Pattern Matching Accuracy Testing
echo "<div class='audit-section'>";
echo "<h2>Step 4: Pattern Matching Accuracy Testing</h2>\n";

echo "<h4>4.1 Basic String Matching Test</h4>\n";

// Test basic string matching for categories without enhanced logic
$basicMatchingTests = [
    'civil' => ['civil', 'CIVIL', 'Civil', 'drept civil', 'litigiu civil'],
    'penal' => ['penal', 'PENAL', 'Penal', 'drept penal', 'infractiune'],
    'comercial' => ['comercial', 'COMERCIAL', 'Comercial', 'drept comercial', 'societate comerciala'],
    'fiscal' => ['fiscal', 'FISCAL', 'Fiscal', 'drept fiscal', 'impozit'],
    'familie' => ['familie', 'FAMILIE', 'Familie', 'drept de familie', 'divort']
];

foreach ($basicMatchingTests as $category => $testTerms) {
    echo "<h5>Testing Basic Matching: {$category}</h5>";

    foreach ($testTerms as $term) {
        // Simulate basic string matching
        $matches = stripos($term, $category) !== false;

        echo "<div class='test-result " . ($matches ? 'pass' : 'warning') . "'>";
        echo "'{$term}' → " . ($matches ? 'MATCH' : 'NO MATCH') . " " . ($matches ? '✅' : '⚠️');
        echo "</div>";
    }
}

echo "</div>";

// Step 5: Romanian Diacritics Testing
echo "<div class='audit-section'>";
echo "<h2>Step 5: Romanian Diacritics Testing</h2>\n";

echo "<h4>5.1 Diacritics Handling Validation</h4>\n";

$diacriticsTests = [
    'muncă' => 'munca',
    'sănătate' => 'sanatate',
    'educație' => 'educatie',
    'proprietate intelectuală' => 'proprietate intelectuala',
    'asigurări' => 'asigurari',
    'construcții' => 'constructii',
    'telecomunicații' => 'telecomunicatii'
];

foreach ($diacriticsTests as $withDiacritics => $withoutDiacritics) {
    // Test if both versions would match
    $matchesWith = stripos($withDiacritics, $withoutDiacritics) !== false;
    $matchesWithout = stripos($withoutDiacritics, $withDiacritics) !== false;

    echo "<div class='test-result " . ($matchesWith || $matchesWithout ? 'pass' : 'warning') . "'>";
    echo "'{$withDiacritics}' ↔ '{$withoutDiacritics}' → ";
    echo ($matchesWith || $matchesWithout ? 'COMPATIBLE' : 'NOT COMPATIBLE') . " ";
    echo ($matchesWith || $matchesWithout ? '✅' : '⚠️');
    echo "</div>";
}

echo "</div>";

// Store the categories for use in subsequent tests
if (!empty($caseCategories)) {
    file_put_contents('temp_case_categories.json', json_encode($caseCategories, JSON_UNESCAPED_UNICODE));
    echo "<div class='test-result info'>";
    echo "ℹ️ Case categories saved for detailed testing in next phase";
    echo "</div>";
}

echo "<div class='audit-section info'>";
echo "<h2>🏁 Case Category Audit Phase 2 Complete</h2>\n";
echo "<p><strong>Completed:</strong></p>";
echo "<ul>";
echo "<li>✅ Case category options inventory</li>";
echo "<li>✅ Current implementation analysis</li>";
echo "<li>✅ Enhanced terminology testing</li>";
echo "<li>✅ Pattern matching accuracy testing</li>";
echo "<li>✅ Romanian diacritics validation</li>";
echo "</ul>";
echo "<p><strong>Next:</strong> Live testing with actual search functionality and recommendations.</p>";
echo "</div>";
?>
