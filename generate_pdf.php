<?php
/**
 * Script pentru generarea PDF-urilor din detaliile dosarelor
 */

// Includere fișiere necesare
require_once 'includes/config.php';
require_once 'includes/functions.php';
require_once 'services/DosarService.php';
require_once 'services/PdfService.php';

// Inițializare variabile
$error = null;
$pdfContent = null;

// Verificare parametri
$numarDosar = isset($_GET['numar']) ? trim($_GET['numar']) : '';
$institutie = isset($_GET['institutie']) ? trim($_GET['institutie']) : '';
$disposition = isset($_GET['disposition']) ? trim($_GET['disposition']) : 'inline'; // 'inline' sau 'attachment'

// Verificare dacă parametrii necesari sunt prezenți
if (empty($numarDosar) || empty($institutie)) {
    $error = "Parametrii necesari pentru generarea PDF lipsesc.";
    $errorCode = "PDF-002";
} else {
    try {
        // Verificăm dacă wkhtmltopdf este disponibil
        $pdfService = new PdfService();
        if (!$pdfService->isAvailable()) {
            throw new Exception("Biblioteca wkhtmltopdf necesară pentru generarea PDF-urilor nu este disponibilă pe server.", 1);
        }

        // Inițializare serviciu pentru căutare
        $dosarService = new DosarService();

        // Obținere detalii dosar
        $dosar = $dosarService->getDetaliiDosar($numarDosar, $institutie);

        if (!$dosar) {
            throw new Exception("Nu s-au putut găsi detalii pentru dosarul specificat.");
        }

        // Generare nume fișier
        $fileName = 'Dosar_' . str_replace(['/', '\\', ' '], '_', $dosar->numar) . '_' . date('d-m-Y') . '.pdf';

        // Generare HTML pentru PDF
        $html = generateHtml($dosar);

        // Opțiuni pentru PDF
        $options = [
            'title' => 'Detalii dosar ' . $dosar->numar,
            'footer-right' => 'Pagina [page] din [topage]',
            'footer-left' => 'Generat la ' . date('d.m.Y H:i'),
            'footer-font-size' => 8,
            'header-html' => generateHeaderHtml($dosar),
            'margin-top' => '25mm',
            'margin-bottom' => '20mm'
        ];

        // Generare PDF
        $pdfContent = $pdfService->generateFromHtml($html, $options);

        // Validăm tipul de disposition (doar 'inline' sau 'attachment' sunt permise)
        if ($disposition !== 'inline' && $disposition !== 'attachment') {
            $disposition = 'inline'; // Valoare implicită dacă parametrul nu este valid
        }

        // Setare headere pentru descărcare sau vizualizare
        header('Content-Type: application/pdf');
        header('Content-Disposition: ' . $disposition . '; filename="' . $fileName . '"');
        header('Content-Length: ' . strlen($pdfContent));
        header('Cache-Control: private, max-age=0, must-revalidate');
        header('Pragma: public');

        // Trimitere conținut PDF
        echo $pdfContent;
        exit;
    } catch (Exception $e) {
        // Determinăm codul de eroare în funcție de tipul excepției
        $errorCode = "PDF-999"; // Cod implicit pentru erori necunoscute

        if ($e->getCode() === 1) {
            // Eroare legată de wkhtmltopdf indisponibil
            $errorCode = "PDF-001";
        } else if (strpos($e->getMessage(), "Nu s-au putut găsi detalii pentru dosarul") !== false) {
            // Eroare legată de dosarul negăsit
            $errorCode = "PDF-003";
        } else if (strpos($e->getMessage(), "Eroare la generarea PDF") !== false) {
            // Eroare în timpul generării PDF
            $errorCode = "PDF-004";
        }

        $error = $e->getMessage();

        // Logare eroare detaliată
        $logDir = __DIR__ . '/logs';
        if (!is_dir($logDir)) {
            mkdir($logDir, 0755, true);
        }

        $logFile = $logDir . '/pdf_errors.log';
        $logData = date('Y-m-d H:i:s') . " - Cod eroare: " . $errorCode . " - " . $e->getMessage();

        // Adăugăm informații suplimentare în log
        $logData .= "\nParametri: numar=" . $numarDosar . ", institutie=" . $institutie;
        $logData .= "\nIP utilizator: " . $_SERVER['REMOTE_ADDR'];
        $logData .= "\nBrowser: " . $_SERVER['HTTP_USER_AGENT'];

        if (isset($pdfService)) {
            $logData .= "\nVersiune wkhtmltopdf: " . $pdfService->getVersion();
        }

        $logData .= "\n";
        file_put_contents($logFile, $logData, FILE_APPEND);
    }
}

// Dacă am ajuns aici, înseamnă că a apărut o eroare
if ($error) {
    header('Content-Type: text/html; charset=utf-8');
    echo '<!DOCTYPE html>
    <html lang="ro">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>Eroare generare PDF</title>
        <link rel="stylesheet" href="https://stackpath.bootstrapcdn.com/bootstrap/4.5.2/css/bootstrap.min.css">
        <style>
            body {
                padding: 20px;
                font-family: Arial, sans-serif;
            }
            .error-container {
                max-width: 600px;
                margin: 50px auto;
                padding: 20px;
                border: 1px solid #dc3545;
                border-radius: 5px;
                background-color: #f8d7da;
                color: #721c24;
            }
            .error-title {
                font-size: 1.5rem;
                font-weight: bold;
                margin-bottom: 15px;
                border-bottom: 1px solid #dc3545;
                padding-bottom: 10px;
            }
            .error-description {
                margin-bottom: 15px;
            }
            .error-instructions {
                background-color: #fff;
                padding: 15px;
                border-radius: 5px;
                margin-bottom: 15px;
                border-left: 4px solid #17a2b8;
            }
            .error-code {
                font-family: monospace;
                background-color: #f8f9fa;
                padding: 5px 10px;
                border-radius: 3px;
                font-size: 0.9rem;
                margin-top: 15px;
                display: inline-block;
            }
            .btn-back {
                margin-top: 20px;
            }
        </style>
    </head>
    <body>
        <div class="error-container">
            <div class="error-title">Eroare la generarea PDF</div>
            <div class="error-description">' . htmlspecialchars($error) . '</div>
            <div class="error-instructions">
                <strong>Ce puteți face:</strong>
                <ul>
                    <li>Încercați din nou mai târziu</li>
                    <li>Verificați dacă parametrii furnizați sunt corecți</li>
                    <li>Încercați să descărcați PDF-ul în loc să îl vizualizați (sau invers)</li>
                    <li>Contactați administratorul sistemului la adresa <a href="mailto:<EMAIL>"><EMAIL></a> pentru rezolvarea acestei probleme</li>
                </ul>
            </div>
            <div class="error-code">Cod eroare: ' . htmlspecialchars($errorCode ?? "PDF-999") . '</div>
            <a href="javascript:history.back()" class="btn btn-primary btn-back">Înapoi</a>
        </div>
    </body>
    </html>';
}

/**
 * Generează HTML-ul pentru antetul PDF-ului
 *
 * @param object $dosar Detaliile dosarului
 * @return string HTML-ul pentru antet
 */
function generateHeaderHtml($dosar) {
    $institutii = getInstanteList();
    $institutieNume = isset($institutii[$dosar->institutie]) ? $institutii[$dosar->institutie] : $dosar->institutie;

    $html = '<!DOCTYPE html>
    <html>
    <head>
        <meta charset="UTF-8">
        <style>
            body {
                font-family: Arial, sans-serif;
                font-size: 10pt;
                color: #333;
                margin: 0;
                padding: 0;
            }
            .header {
                text-align: center;
                padding: 5mm;
                border-bottom: 1px solid #ddd;
            }
            .header h1 {
                font-size: 14pt;
                margin: 0;
                padding: 0;
                color: #007bff;
            }
            .header p {
                font-size: 10pt;
                margin: 5px 0 0 0;
                color: #666;
            }
        </style>
    </head>
    <body>
        <div class="header">
            <h1>Detalii dosar: ' . htmlspecialchars($dosar->numar) . '</h1>
            <p>' . htmlspecialchars($institutieNume) . '</p>
        </div>
    </body>
    </html>';

    return $html;
}

/**
 * Generează HTML-ul pentru conținutul PDF-ului
 *
 * @param object $dosar Detaliile dosarului
 * @return string HTML-ul pentru conținut
 */
function generateHtml($dosar) {
    $institutii = getInstanteList();
    $institutieNume = isset($institutii[$dosar->institutie]) ? $institutii[$dosar->institutie] : $dosar->institutie;

    $html = '<!DOCTYPE html>
    <html>
    <head>
        <meta charset="UTF-8">
        <style>
            body {
                font-family: Arial, sans-serif;
                font-size: 10pt;
                color: #333;
                line-height: 1.4;
            }
            h1 {
                font-size: 16pt;
                color: #007bff;
                margin-bottom: 10px;
            }
            h2 {
                font-size: 14pt;
                color: #0056b3;
                margin-top: 20px;
                margin-bottom: 10px;
                padding-bottom: 5px;
                border-bottom: 1px solid #ddd;
            }
            table {
                width: 100%;
                border-collapse: collapse;
                margin-bottom: 20px;
            }
            th, td {
                border: 1px solid #ddd;
                padding: 8px;
                text-align: left;
            }
            th {
                background-color: #f8f9fa;
                font-weight: bold;
            }
            .info-section {
                margin-bottom: 15px;
            }
            .info-label {
                font-weight: bold;
            }
            .footer {
                font-size: 8pt;
                text-align: center;
                color: #666;
                margin-top: 20px;
                padding-top: 10px;
                border-top: 1px solid #ddd;
            }
        </style>
    </head>
    <body>';

    // Informații generale
    $html .= '<div class="info-section">
        <h2>Informații generale</h2>
        <table>
            <tr>
                <td class="info-label">Număr dosar:</td>
                <td>' . htmlspecialchars($dosar->numar) . '</td>
                <td class="info-label">Instanță:</td>
                <td>' . htmlspecialchars($institutieNume) . '</td>
            </tr>';

    if (!empty($dosar->numarVechi)) {
        $html .= '<tr>
            <td class="info-label">Număr vechi:</td>
            <td>' . htmlspecialchars($dosar->numarVechi) . '</td>
            <td class="info-label"></td>
            <td></td>
        </tr>';
    }

    $html .= '<tr>
            <td class="info-label">Data:</td>
            <td>' . htmlspecialchars($dosar->data) . '</td>
            <td class="info-label">Obiect:</td>
            <td>' . htmlspecialchars($dosar->obiect) . '</td>
        </tr>
        <tr>
            <td class="info-label">Stadiu procesual:</td>
            <td>' . htmlspecialchars($dosar->stadiuProcesualNume) . '</td>
            <td class="info-label">Categorie caz:</td>
            <td>' . htmlspecialchars($dosar->categorieCazNume) . '</td>
        </tr>
        <tr>
            <td class="info-label">Data ultimei modificări:</td>
            <td>' . htmlspecialchars($dosar->dataModificare) . '</td>
            <td class="info-label"></td>
            <td></td>
        </tr>
    </table>
    </div>';

    // Părțile implicate
    $html .= '<div class="info-section">
        <h2>Părți implicate</h2>';

    if (!empty($dosar->parti)) {
        $html .= '<table>
            <thead>
                <tr>
                    <th>Nume</th>
                    <th>Calitate</th>
                </tr>
            </thead>
            <tbody>';

        foreach ($dosar->parti as $parte) {
            $html .= '<tr>
                <td>' . htmlspecialchars($parte['nume']) . '</td>
                <td>' . htmlspecialchars($parte['calitate']) . '</td>
            </tr>';
        }

        $html .= '</tbody>
        </table>';
    } else {
        $html .= '<p>Nu există informații despre părțile implicate.</p>';
    }

    $html .= '</div>';

    // Ședințele de judecată
    $html .= '<div class="info-section">
        <h2>Ședințe de judecată</h2>';

    if (!empty($dosar->sedinte)) {
        $html .= '<table>
            <thead>
                <tr>
                    <th>Data ședință</th>
                    <th>Ora</th>
                    <th>Complet</th>
                    <th>Soluție</th>
                </tr>
            </thead>
            <tbody>';

        foreach ($dosar->sedinte as $sedinta) {
            $html .= '<tr>
                <td>' . htmlspecialchars($sedinta['data']) . '</td>
                <td>' . htmlspecialchars($sedinta['ora'] ?: '-') . '</td>
                <td>' . htmlspecialchars($sedinta['complet'] ?: '-') . '</td>
                <td>' . htmlspecialchars($sedinta['solutie'] ?: '-') . '</td>
            </tr>';
        }

        $html .= '</tbody>
        </table>';
    } else {
        $html .= '<p>Nu există informații despre ședințele de judecată.</p>';
    }

    $html .= '</div>';

    // Căile de atac
    $html .= '<div class="info-section">
        <h2>Căi de atac</h2>';

    if (!empty($dosar->caiAtac)) {
        $html .= '<table>
            <thead>
                <tr>
                    <th>Data declarare</th>
                    <th>Parte declaratoare</th>
                    <th>Tip cale atac</th>
                    <th>Dosar instanță superioară</th>
                </tr>
            </thead>
            <tbody>';

        foreach ($dosar->caiAtac as $caleAtac) {
            $html .= '<tr>
                <td>' . htmlspecialchars($caleAtac['dataDeclarare'] ?: '-') . '</td>
                <td>' . htmlspecialchars($caleAtac['parteDeclaratoare'] ?: '-') . '</td>
                <td>' . htmlspecialchars($caleAtac['tipCaleAtac'] ?: '-') . '</td>
                <td>' . htmlspecialchars($caleAtac['numarDosarInstantaSuperior'] ?: '-') . '</td>
            </tr>';
        }

        $html .= '</tbody>
        </table>';
    } else {
        $html .= '<p>Nu există informații despre căile de atac pentru acest dosar.</p>';
    }

    $html .= '</div>';

    // Notă de subsol
    $html .= '<div class="footer">
        <p>Document generat de Portalul Dosare Judecătorești. Informațiile sunt preluate direct de la Portalul Instanțelor de Judecată.</p>
        <p>Acest document nu constituie un document oficial și este generat doar în scop informativ.</p>
    </div>';

    $html .= '</body>
    </html>';

    return $html;
}
