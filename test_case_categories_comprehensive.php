<?php
/**
 * Comprehensive testing of all case category filtering functionality
 */

require_once 'includes/config.php';
require_once 'includes/functions.php';
require_once 'src/Services/DosarService.php';

echo "<h1>🧪 Case Category Filtering - Comprehensive Testing</h1>\n";
echo "<style>
    .test-section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
    .success { background-color: #d4edda; border-color: #c3e6cb; }
    .error { background-color: #f8d7da; border-color: #f5c6cb; }
    .warning { background-color: #fff3cd; border-color: #ffeaa7; }
    .info { background-color: #d1ecf1; border-color: #bee5eb; }
    .test-result { margin: 5px 0; padding: 8px; border-radius: 3px; }
    .pass { background-color: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
    .fail { background-color: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
    .enhanced { background-color: #e7f3ff; border: 1px solid #b3d9ff; color: #0056b3; }
    .basic { background-color: #f8f9fa; border: 1px solid #dee2e6; color: #495057; }
    .needs-enhancement { background-color: #fff3cd; border: 1px solid #ffeaa7; color: #856404; }
    .category-summary { background: #f8f9fa; padding: 10px; border-radius: 3px; margin: 10px 0; border-left: 4px solid #007bff; }
</style>\n";

echo "<div class='test-section info'>";
echo "<h2>🎯 Comprehensive Case Category Testing</h2>\n";
echo "<p><strong>Testing all case categories from the advanced search form:</strong></p>";
echo "</div>";

// Define all case categories from the form
$allCaseCategories = [
    'civil' => [
        'label' => 'Civil',
        'description' => 'Civil Law',
        'has_enhanced' => false,
        'test_terms' => ['civil', 'drept civil', 'litigiu civil', 'proces civil', 'contracte civile'],
        'romanian_terms' => ['civil', 'civilă', 'civile', 'drept civil', 'proces civil']
    ],
    'penal' => [
        'label' => 'Penal',
        'description' => 'Criminal Law',
        'has_enhanced' => false,
        'test_terms' => ['penal', 'drept penal', 'infractiune', 'proces penal', 'crimă'],
        'romanian_terms' => ['penal', 'penală', 'penale', 'infracțiune', 'infracțiuni']
    ],
    'comercial' => [
        'label' => 'Comercial',
        'description' => 'Commercial Law',
        'has_enhanced' => false,
        'test_terms' => ['comercial', 'drept comercial', 'societate comerciala', 'afaceri', 'comert'],
        'romanian_terms' => ['comercial', 'comercială', 'comerciale', 'societate comercială', 'comerț']
    ],
    'administrativ' => [
        'label' => 'Administrativ',
        'description' => 'Administrative Law',
        'has_enhanced' => false,
        'test_terms' => ['administrativ', 'drept administrativ', 'autoritate publica', 'act administrativ'],
        'romanian_terms' => ['administrativ', 'administrativă', 'administrative', 'autoritate publică']
    ],
    'fiscal' => [
        'label' => 'Fiscal',
        'description' => 'Tax Law',
        'has_enhanced' => false,
        'test_terms' => ['fiscal', 'drept fiscal', 'impozit', 'taxe', 'contributii'],
        'romanian_terms' => ['fiscal', 'fiscală', 'fiscale', 'impozite', 'contribuții']
    ],
    'munca' => [
        'label' => 'Muncă',
        'description' => 'Labor Law',
        'has_enhanced' => true,
        'test_terms' => ['munca', 'muncă', 'salarial', 'angajat', 'contract de munca', 'concediere'],
        'romanian_terms' => ['muncă', 'munci', 'salarială', 'angajați', 'contract de muncă']
    ],
    'familie' => [
        'label' => 'Familie',
        'description' => 'Family Law',
        'has_enhanced' => false,
        'test_terms' => ['familie', 'drept de familie', 'divort', 'casatorie', 'custodie'],
        'romanian_terms' => ['familie', 'familii', 'divorț', 'căsătorie', 'custodie']
    ],
    'contencios_administrativ' => [
        'label' => 'Contencios administrativ',
        'description' => 'Administrative Litigation',
        'has_enhanced' => true,
        'test_terms' => ['contencios administrativ', 'contencios', 'litigiu administrativ', 'act administrativ'],
        'romanian_terms' => ['contencios administrativ', 'contenciosul administrativ', 'litigiu administrativ']
    ]
];

try {
    $dosarService = new \App\Services\DosarService();
    
    // Test 1: Implementation Status Analysis
    echo "<div class='test-section'>";
    echo "<h2>Test 1: Implementation Status Analysis</h2>\n";
    
    $enhancedCount = 0;
    $basicCount = 0;
    
    foreach ($allCaseCategories as $categoryValue => $categoryInfo) {
        if ($categoryInfo['has_enhanced']) {
            $enhancedCount++;
            echo "<div class='test-result enhanced'>";
            echo "🔧 <strong>{$categoryValue}</strong> ({$categoryInfo['label']}): Enhanced terminology matching";
            echo "</div>";
        } else {
            $basicCount++;
            echo "<div class='test-result basic'>";
            echo "📝 <strong>{$categoryValue}</strong> ({$categoryInfo['label']}): Basic string matching";
            echo "</div>";
        }
    }
    
    echo "<div class='category-summary'>";
    echo "<strong>Summary:</strong> {$enhancedCount} categories with enhanced matching, {$basicCount} with basic matching";
    echo "</div>";
    
    echo "</div>";
    
    // Test 2: Enhanced Categories Validation
    echo "<div class='test-section'>";
    echo "<h2>Test 2: Enhanced Categories Validation</h2>\n";
    
    foreach ($allCaseCategories as $categoryValue => $categoryInfo) {
        if ($categoryInfo['has_enhanced']) {
            echo "<h4>Testing Enhanced Category: {$categoryValue} ({$categoryInfo['label']})</h4>\n";
            
            foreach ($categoryInfo['test_terms'] as $term) {
                // Simulate the enhanced matching logic
                $matches = false;
                
                if ($categoryValue === 'munca') {
                    $laborTerms = ['munca', 'muncă', 'salarial', 'salariat', 'angajat', 'angajator',
                                  'contract de munca', 'contract de muncă', 'individual de munca',
                                  'individual de muncă', 'concediere', 'licențiere', 'despăgubiri',
                                  'daune morale', 'discriminare', 'hărțuire', 'overtime', 'ore suplimentare'];
                    
                    foreach ($laborTerms as $laborTerm) {
                        if (stripos($term, $laborTerm) !== false) {
                            $matches = true;
                            break;
                        }
                    }
                } elseif ($categoryValue === 'contencios_administrativ') {
                    $adminTerms = ['contencios administrativ', 'contencios', 'administrativ', 'administrative',
                                  'litigiu administrativ', 'drept administrativ', 'admin', 'contenciosul administrativ',
                                  'act administrativ', 'decizie administrativa', 'decizie administrativă',
                                  'autoritate publica', 'autoritate publică', 'instituție publică', 'institutie publica',
                                  'anulare act', 'anularea actului', 'obligarea la', 'constatarea nulității',
                                  'repararea prejudiciului', 'daune-interese', 'contencios fiscal',
                                  'contencios urbanistic', 'contencios în materie', 'recurs administrativ'];
                    
                    foreach ($adminTerms as $adminTerm) {
                        if (stripos($term, $adminTerm) !== false) {
                            $matches = true;
                            break;
                        }
                    }
                }
                
                echo "<div class='test-result " . ($matches ? 'pass' : 'fail') . "'>";
                echo "'{$term}' → " . ($matches ? 'MATCH ✅' : 'NO MATCH ❌');
                echo "</div>";
            }
        }
    }
    
    echo "</div>";
    
    // Test 3: Basic String Matching Validation
    echo "<div class='test-section'>";
    echo "<h2>Test 3: Basic String Matching Validation</h2>\n";
    
    foreach ($allCaseCategories as $categoryValue => $categoryInfo) {
        if (!$categoryInfo['has_enhanced']) {
            echo "<h4>Testing Basic Category: {$categoryValue} ({$categoryInfo['label']})</h4>\n";
            
            foreach ($categoryInfo['test_terms'] as $term) {
                // Simulate basic string matching
                $matches = stripos($term, $categoryValue) !== false;
                
                echo "<div class='test-result " . ($matches ? 'pass' : 'warning') . "'>";
                echo "'{$term}' → " . ($matches ? 'MATCH ✅' : 'NO MATCH ⚠️');
                echo "</div>";
            }
        }
    }
    
    echo "</div>";
    
    // Test 4: Romanian Diacritics Testing
    echo "<div class='test-section'>";
    echo "<h2>Test 4: Romanian Diacritics Testing</h2>\n";
    
    foreach ($allCaseCategories as $categoryValue => $categoryInfo) {
        echo "<h4>Testing Diacritics: {$categoryValue} ({$categoryInfo['label']})</h4>\n";
        
        foreach ($categoryInfo['romanian_terms'] as $term) {
            // Test if the term would match the category
            $matches = false;
            
            if ($categoryInfo['has_enhanced']) {
                // For enhanced categories, test against their enhanced logic
                if ($categoryValue === 'munca') {
                    $laborTerms = ['munca', 'muncă', 'salarial', 'salariat', 'angajat', 'angajator'];
                    foreach ($laborTerms as $laborTerm) {
                        if (stripos($term, $laborTerm) !== false) {
                            $matches = true;
                            break;
                        }
                    }
                } elseif ($categoryValue === 'contencios_administrativ') {
                    $adminTerms = ['contencios administrativ', 'contencios', 'administrativ'];
                    foreach ($adminTerms as $adminTerm) {
                        if (stripos($term, $adminTerm) !== false) {
                            $matches = true;
                            break;
                        }
                    }
                }
            } else {
                // For basic categories, test basic string matching
                $matches = stripos($term, $categoryValue) !== false;
            }
            
            echo "<div class='test-result " . ($matches ? 'pass' : 'warning') . "'>";
            echo "'{$term}' → " . ($matches ? 'COMPATIBLE ✅' : 'NOT COMPATIBLE ⚠️');
            echo "</div>";
        }
    }
    
    echo "</div>";
    
} catch (Exception $e) {
    echo "<div class='test-section error'>";
    echo "<h2>❌ Critical Error</h2>\n";
    echo "<div class='test-result fail'>";
    echo "Error: " . htmlspecialchars($e->getMessage());
    echo "</div>";
    echo "</div>";
}

echo "<div class='test-section info'>";
echo "<h2>🏁 Comprehensive Case Category Testing Complete</h2>\n";
echo "<p><strong>Next:</strong> Live search testing and enhancement recommendations.</p>";
echo "</div>";
?>
