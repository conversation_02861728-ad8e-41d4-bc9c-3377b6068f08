<?php
/**
 * Test script for enhanced multi-word party name search with exact phrase matching
 */

// Include the bulk search functions
require_once 'bulk_search.php';

echo "<h1>Enhanced Multi-Word Party Name Search Test</h1>";
echo "<style>
    .test-section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; background: #f9f9f9; }
    .test-case { margin: 10px 0; padding: 10px; border-left: 3px solid #007bff; background: #f8f9fa; }
    .pass { border-left-color: #28a745 !important; background: #d4edda !important; }
    .fail { border-left-color: #dc3545 !important; background: #f8d7da !important; }
    .result { font-family: monospace; background: #e9ecef; padding: 5px; border-radius: 3px; }
</style>";

// Test data simulating real party structures
$testParties = [
    ['nume' => 'POPA ROŞU RALUCA IOANA', 'calitate' => 'Reclamant'],
    ['nume' => 'MAIER RADU MIHAI', 'calitate' => '<PERSON>â<PERSON><PERSON>t'],
    ['nume' => 'MAIER RADU', 'calitate' => 'Martor'],
    ['nume' => 'SC EXEMPLU SRL', 'calitate' => 'Terț'],
    ['nume' => 'IONESCU MARIA ELENA', 'calitate' => 'Intervenient']
];

// Test cases for phrase matching
$testCases = [
    [
        'name' => 'Exact phrase match with quotes',
        'search' => '"MAIER RADU MIHAI"',
        'expected_party' => 'MAIER RADU MIHAI',
        'expected_type' => 'exact-phrase',
        'description' => 'Should find exact phrase "MAIER RADU MIHAI" and highlight the complete phrase'
    ],
    [
        'name' => 'Partial phrase match with quotes',
        'search' => '"MAIER RADU"',
        'expected_party' => 'MAIER RADU MIHAI',
        'expected_type' => 'exact-phrase',
        'description' => 'Should find "MAIER RADU" within "MAIER RADU MIHAI" as phrase match'
    ],
    [
        'name' => 'Exact single name match with quotes',
        'search' => '"MAIER RADU"',
        'expected_party' => 'MAIER RADU',
        'expected_type' => 'exact-phrase',
        'description' => 'Should prefer exact match "MAIER RADU" over partial match in "MAIER RADU MIHAI"'
    ],
    [
        'name' => 'Regular search without quotes',
        'search' => 'MAIER RADU',
        'expected_party' => 'MAIER RADU',
        'expected_type' => 'exact',
        'description' => 'Should use regular matching logic without quotes'
    ],
    [
        'name' => 'Partial match without quotes',
        'search' => 'MAIER',
        'expected_party' => 'MAIER RADU MIHAI',
        'expected_type' => 'partial',
        'description' => 'Should find partial match for single word'
    ],
    [
        'name' => 'Romanian diacritics with quotes',
        'search' => '"POPA ROSU RALUCA"',
        'expected_party' => 'POPA ROŞU RALUCA IOANA',
        'expected_type' => 'exact-phrase',
        'description' => 'Should handle Romanian diacritics in phrase matching'
    ],
    [
        'name' => 'No match with quotes',
        'search' => '"INEXISTENT PERSON"',
        'expected_party' => null,
        'expected_type' => 'none',
        'description' => 'Should return null for non-existent phrase'
    ]
];

echo "<div class='test-section'>";
echo "<h2>Test Data</h2>";
echo "<h3>Available Parties:</h3>";
echo "<ul>";
foreach ($testParties as $index => $party) {
    echo "<li><strong>{$party['nume']}</strong> ({$party['calitate']})</li>";
}
echo "</ul>";
echo "</div>";

echo "<div class='test-section'>";
echo "<h2>Test Results</h2>";

$passCount = 0;
$totalTests = count($testCases);

foreach ($testCases as $index => $testCase) {
    echo "<div class='test-case' id='test-{$index}'>";
    echo "<h4>Test " . ($index + 1) . ": {$testCase['name']}</h4>";
    echo "<p><strong>Description:</strong> {$testCase['description']}</p>";
    echo "<p><strong>Search Term:</strong> <code>{$testCase['search']}</code></p>";
    
    // Test findMatchingParty function
    $matchingParty = findMatchingParty($testParties, $testCase['search']);
    $actualPartyName = $matchingParty ? $matchingParty['nume'] : null;
    
    // Test getMatchType function
    $isExactPhraseSearch = preg_match('/^"(.+)"$/', trim($testCase['search']));
    $cleanSearchTerm = $isExactPhraseSearch ? preg_replace('/^"(.+)"$/', '$1', trim($testCase['search'])) : $testCase['search'];
    $actualMatchType = $actualPartyName ? getMatchType($actualPartyName, $cleanSearchTerm, $isExactPhraseSearch) : 'none';
    
    // Test highlighting function
    $highlighted = '';
    if ($actualPartyName) {
        $highlighted = highlightMatchingPartyName($actualPartyName, $testCase['search'], 'numeParte');
    }
    
    // Check if test passes
    $partyMatch = ($actualPartyName === $testCase['expected_party']);
    $typeMatch = ($actualMatchType === $testCase['expected_type']);
    $testPassed = $partyMatch && $typeMatch;
    
    if ($testPassed) {
        $passCount++;
        echo "<div class='test-case pass'>";
        echo "<strong>✅ PASS</strong>";
    } else {
        echo "<div class='test-case fail'>";
        echo "<strong>❌ FAIL</strong>";
    }
    
    echo "<div class='result'>";
    echo "<strong>Expected:</strong> Party='{$testCase['expected_party']}', Type='{$testCase['expected_type']}'<br>";
    echo "<strong>Actual:</strong> Party='{$actualPartyName}', Type='{$actualMatchType}'<br>";
    if (!empty($highlighted)) {
        echo "<strong>Highlighted:</strong> {$highlighted}<br>";
    }
    echo "</div>";
    
    // Additional debugging info
    echo "<details>";
    echo "<summary>Debug Info</summary>";
    echo "<pre>";
    echo "Is Exact Phrase Search: " . ($isExactPhraseSearch ? 'Yes' : 'No') . "\n";
    echo "Clean Search Term: '{$cleanSearchTerm}'\n";
    echo "Matching Party Object: " . print_r($matchingParty, true);
    echo "</pre>";
    echo "</details>";
    
    echo "</div>";
    echo "</div>";
}

echo "</div>";

// Summary
echo "<div class='test-section'>";
echo "<h2>Test Summary</h2>";
echo "<p><strong>Tests Passed:</strong> {$passCount} / {$totalTests}</p>";

if ($passCount === $totalTests) {
    echo "<div class='test-case pass'>";
    echo "<h3>🎉 All Tests Passed!</h3>";
    echo "<p>The enhanced multi-word party name search with exact phrase matching is working correctly.</p>";
    echo "</div>";
} else {
    echo "<div class='test-case fail'>";
    echo "<h3>⚠️ Some Tests Failed</h3>";
    echo "<p>Please review the failed tests and fix the implementation.</p>";
    echo "</div>";
}
echo "</div>";

echo "<hr>";
echo "<p><a href='bulk_search.php'>← Back to Bulk Search</a></p>";
?>
