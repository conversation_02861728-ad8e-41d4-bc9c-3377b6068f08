<?php

/**
 * Portal Judiciar - Pagina de detalii dosar
 */

// Încărcăm bootstrap-ul aplicației
require_once dirname(__DIR__) . '/bootstrap.php';

// Importăm clasele necesare
use App\Helpers\TemplateEngine;
use App\Services\DosarService;

// Inițializăm motorul de șabloane
$templateEngine = new TemplateEngine();

// Lista instanțelor
$instante = [
    'JudecatoriaSECTORUL1BUCURESTI' => 'Judecătoria Sectorului 1 București',
    'JudecatoriaSECTORUL2BUCURESTI' => 'Judecătoria Sectorului 2 București',
    'JudecatoriaSECTORUL3BUCURESTI' => 'Judecătoria Sectorului 3 București',
    'JudecatoriaSECTORUL4BUCURESTI' => 'Judecătoria Sectorului 4 București',
    'JudecatoriaSECTORUL5BUCURESTI' => 'Judecătoria Sectorului 5 București',
    'JudecatoriaSECTORUL6BUCURESTI' => 'Judecătoria Sectorului 6 București',
    'TribunalulBUCURESTI' => 'Tribunalul București',
    'CurteadeApelBUCURESTI' => 'Curtea de Apel București',
    'JudecatoriaCLUJNAPOCA' => 'Judecătoria Cluj-Napoca',
    'TribunalulCLUJ' => 'Tribunalul Cluj',
    'CurteadeApelCLUJ' => 'Curtea de Apel Cluj',
    'JudecatoriaCONSTANTA' => 'Judecătoria Constanța',
    'TribunalulCONSTANTA' => 'Tribunalul Constanța',
    'CurteadeApelCONSTANTA' => 'Curtea de Apel Constanța',
    'JudecatoriaTIMISOARA' => 'Judecătoria Timișoara',
    'TribunalulTIMIS' => 'Tribunalul Timiș',
    'CurteadeApelTIMISOARA' => 'Curtea de Apel Timișoara',
    'JudecatoriaIASI' => 'Judecătoria Iași',
    'TribunalulIASI' => 'Tribunalul Iași',
    'CurteadeApelIASI' => 'Curtea de Apel Iași',
    'JudecatoriaBRASOV' => 'Judecătoria Brașov',
    'TribunalulBRASOV' => 'Tribunalul Brașov',
    'CurteadeApelBRASOV' => 'Curtea de Apel Brașov',
    'InaltaCurtedeCasatiesiJustitie' => 'Înalta Curte de Casație și Justiție'
];

// Parametrii pentru detalii dosar
$numarDosar = $_GET['numar'] ?? '';
$institutie = $_GET['institutie'] ?? '';

// Verificăm dacă avem parametrii necesari
if (empty($numarDosar) || empty($institutie)) {
    // Adăugăm un mesaj flash cu eroarea
    TemplateEngine::addFlashMessage('danger', 'Numărul dosarului și instituția sunt obligatorii pentru vizualizarea detaliilor.');
    
    // Redirecționăm către pagina principală
    header('Location: index.php');
    exit;
}

// Detaliile dosarului
$dosar = null;

try {
    // Inițializăm serviciul de dosare
    $dosarService = new DosarService();
    
    // Obținem detaliile dosarului
    $dosar = $dosarService->getDetaliiDosar($numarDosar, $institutie);
    
    // Verificăm dacă am găsit dosarul
    if (empty((array)$dosar)) {
        // Adăugăm un mesaj flash cu eroarea
        TemplateEngine::addFlashMessage('warning', 'Nu au fost găsite detalii pentru dosarul specificat.');
        
        // Redirecționăm către pagina principală
        header('Location: index.php');
        exit;
    }
} catch (Exception $e) {
    // Adăugăm un mesaj flash cu eroarea
    TemplateEngine::addFlashMessage('danger', 'Eroare la obținerea detaliilor dosarului: ' . $e->getMessage());
    
    // Logăm eroarea
    error_log('Eroare la obținerea detaliilor dosarului: ' . $e->getMessage());
    
    // Redirecționăm către pagina principală
    header('Location: index.php');
    exit;
}

// Datele pentru șablon
$data = [
    'instante' => $instante,
    'dosar' => $dosar
];

// Afișăm șablonul
echo $templateEngine->render('detalii_dosar.twig', $data);
