<?php
/**
 * Pagina cu informații despre aplicație
 */

// Includere fișiere necesare
require_once 'includes/config.php';
?>
<!DOCTYPE html>
<html lang="ro">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Despre - Portal Judiciar</title>

    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">

    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">

    <!-- Custom CSS -->
    <link href="assets/css/style.css" rel="stylesheet">
    <link href="assets/css/responsive.css" rel="stylesheet">

    <style>
        /* About Page Specific Styles - Matching bulk_search.php design */
        :root {
            --primary-blue: #007bff;
            --secondary-blue: #2c3e50;
            --light-bg: #f8f9fa;
            --border-color: #dee2e6;
            --success-color: #28a745;
            --warning-color: #ffc107;
            --danger-color: #dc3545;
            --info-color: #17a2b8;
        }

        body {
            background-color: var(--light-bg);
            font-family: 'Roboto', 'Segoe UI', Arial, sans-serif;
        }

        /* Card styling matching bulk_search.php */
        .streamlined-card {
            border: none;
            border-radius: 8px;
            box-shadow: 0 4px 12px rgba(0, 123, 255, 0.15);
            margin-bottom: 2rem;
            overflow: hidden;
            background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
        }

        .streamlined-card .card-header {
            background: linear-gradient(135deg, var(--primary-blue) 0%, var(--secondary-blue) 100%);
            color: white;
            border-bottom: none;
            padding: 1.25rem 1.5rem;
        }

        .streamlined-card .card-body {
            padding: 2rem;
        }

        /* Navigation Bar Styles */
        .navbar {
            background-color: #f8f9fa !important;
            border-bottom: 2px solid #007bff;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
            padding: 0.75rem 0;
        }

        .navbar-brand {
            font-weight: 600;
            color: #2c3e50 !important;
            font-size: 1.25rem;
            transition: all 0.3s ease;
        }

        .navbar-brand:hover {
            color: #007bff !important;
            transform: translateY(-1px);
        }

        .navbar-brand i {
            color: #007bff;
            margin-right: 0.5rem;
        }

        .nav-link {
            color: #495057 !important;
            font-weight: 500;
            padding: 0.5rem 1rem !important;
            border-radius: 6px;
            transition: all 0.3s ease;
            margin: 0 0.25rem;
        }

        .nav-link:hover {
            color: #007bff !important;
            background-color: rgba(0, 123, 255, 0.1);
            transform: translateY(-1px);
        }

        .nav-link.active,
        .nav-item.active .nav-link {
            color: #007bff !important;
            background-color: rgba(0, 123, 255, 0.15);
            font-weight: 600;
        }

        .nav-link i {
            margin-right: 0.5rem;
            width: 16px;
            text-align: center;
        }

        /* Responsive navigation */
        @media (max-width: 991.98px) {
            .navbar-nav {
                margin-top: 1rem;
                padding-top: 1rem;
                border-top: 1px solid #dee2e6;
            }

            .nav-link {
                margin: 0.25rem 0;
                text-align: center;
            }
        }

        @media (max-width: 575.98px) {
            .navbar-brand {
                font-size: 1.1rem;
            }

            .nav-link {
                padding: 0.75rem 1rem !important;
            }
        }

        /* About page specific styling */
        .about-section {
            margin-bottom: 2rem;
        }

        .about-section h3 {
            color: var(--secondary-blue);
            font-weight: 600;
            margin-bottom: 1rem;
        }

        .about-section h4 {
            color: var(--primary-blue);
            font-weight: 600;
            margin-bottom: 0.75rem;
        }

        .about-section ul {
            padding-left: 1.5rem;
        }

        .about-section li {
            margin-bottom: 0.5rem;
            line-height: 1.6;
        }

        .about-section p {
            line-height: 1.6;
            margin-bottom: 1rem;
        }

        .about-section a {
            color: var(--primary-blue);
            text-decoration: none;
            font-weight: 500;
        }

        .about-section a:hover {
            color: var(--secondary-blue);
            text-decoration: underline;
        }

        .alert-info {
            background: linear-gradient(135deg, rgba(23, 162, 184, 0.1) 0%, rgba(23, 162, 184, 0.05) 100%);
            border: 1px solid rgba(23, 162, 184, 0.2);
            border-radius: 8px;
        }

        .alert-info .alert-heading {
            color: var(--info-color);
            font-weight: 600;
        }

        /* Loading Overlay Styles */
        .loading-overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.7);
            display: flex;
            justify-content: center;
            align-items: center;
            z-index: 9999;
        }

        .loading-content {
            background-color: white;
            padding: 2rem;
            border-radius: 8px;
            text-align: center;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
            max-width: 400px;
            width: 90%;
        }

        .loading-spinner {
            border: 4px solid #f3f3f3;
            border-top: 4px solid var(--primary-blue);
            border-radius: 50%;
            width: 40px;
            height: 40px;
            animation: spin 1s linear infinite;
            margin: 0 auto 1rem;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        /* Notification Container Styles */
        .notification-container {
            position: fixed;
            top: 20px;
            right: 20px;
            z-index: 1050;
            max-width: 400px;
        }

        .notification-container .alert {
            margin-bottom: 0;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
            border-radius: 8px;
        }
    </style>
</head>
<body>
    <!-- Loading Overlay -->
    <div class="loading-overlay" id="loadingOverlay" style="display: none;">
        <div class="loading-content">
            <div class="loading-spinner"></div>
            <h5 id="loadingMessage">Se încarcă...</h5>
            <p id="loadingSubmessage">Vă rugăm să așteptați...</p>
        </div>
    </div>

    <!-- Notification Container -->
    <div id="notificationContainer" class="notification-container" style="display: none;">
        <div id="notification" class="alert" role="alert"></div>
    </div>

    <!-- Navigation Bar -->
    <nav class="navbar navbar-expand-lg navbar-light bg-light border-bottom">
        <div class="container">
            <a class="navbar-brand" href="index.php">
                <i class="fas fa-gavel me-2"></i>
                Portal Judiciar
            </a>

            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav" aria-controls="navbarNav" aria-expanded="false" aria-label="Toggle navigation">
                <span class="navbar-toggler-icon"></span>
            </button>

            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav ms-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="index.php">
                            <i class="fas fa-search me-1"></i>
                            Căutare Simplă
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="bulk_search.php">
                            <i class="fas fa-search-plus me-1"></i>
                            Căutare bulk
                        </a>
                    </li>
                    <li class="nav-item active">
                        <a class="nav-link" href="about.php">
                            <i class="fas fa-info-circle me-1"></i>
                            Despre
                            <span class="visually-hidden">(current)</span>
                        </a>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <div class="container mt-4">
        <div class="row">
            <div class="col-md-12">
                <!-- Main About Section -->
                <div class="card streamlined-card">
                    <div class="card-header">
                        <h2 class="h4 mb-0">
                            <i class="fas fa-info-circle me-2"></i>
                            Despre Portal Dosare Judecătorești
                        </h2>
                    </div>
                    <div class="card-body about-section">
                        <h3>Descriere</h3>
                        <p>Portalul Dosare Judecătorești este o aplicație web care permite căutarea și vizualizarea informațiilor despre dosarele judecătorești din România, utilizând API-ul oficial al Portalului Instanțelor de Judecată.</p>

                        <h3 class="mt-4">Funcționalități</h3>
                        <ul>
                            <li>Căutare dosare după număr dosar</li>
                            <li>Căutare dosare după nume parte</li>
                            <li>Căutare dosare după obiect</li>
                            <li>Căutare bulk pentru multiple criterii simultan</li>
                            <li>Filtrare rezultate după instanță judecătorească</li>
                            <li>Filtrare rezultate după interval de date</li>
                            <li>Vizualizare detaliată a dosarelor, inclusiv părți implicate, ședințe de judecată și căi de atac</li>
                            <li>Export rezultate în format CSV și Excel</li>
                            <li>Design responsive optimizat pentru mobile</li>
                        </ul>

                        <h3 class="mt-4">Sursa datelor</h3>
                        <p>Toate informațiile afișate sunt preluate direct de la <a href="http://portal.just.ro" target="_blank">Portalul Instanțelor de Judecată</a> prin intermediul serviciului web SOAP disponibil la adresa <a href="http://portalquery.just.ro/query.asmx?op=CautareDosare2" target="_blank">http://portalquery.just.ro/query.asmx?op=CautareDosare2</a>.</p>

                        <div class="alert alert-info mt-4">
                            <h4 class="alert-heading">Notă importantă</h4>
                            <p>Informațiile afișate sunt furnizate cu titlu informativ și nu constituie documente oficiale. Pentru informații oficiale complete, vă recomandăm să consultați direct instanța de judecată.</p>
                        </div>
                    </div>
                </div>
            
                <!-- Usage Guide Section -->
                <div class="card streamlined-card">
                    <div class="card-header">
                        <h3 class="h4 mb-0">
                            <i class="fas fa-question-circle me-2"></i>
                            Cum să utilizați portalul
                        </h3>
                    </div>
                    <div class="card-body about-section">
                        <h4>Căutare simplă</h4>
                        <p>Pentru a căuta un dosar, introduceți numărul dosarului, numele unei părți implicate sau obiectul dosarului în formularul de căutare de pe pagina principală și apăsați butonul "Caută".</p>

                        <h4 class="mt-3">Căutare bulk</h4>
                        <p>Pentru căutări multiple simultane, utilizați funcția de căutare bulk care vă permite să introduceți mai mulți termeni de căutare deodată și să exportați rezultatele în format CSV sau Excel.</p>

                        <h4 class="mt-3">Căutare avansată</h4>
                        <p>Pentru o căutare mai precisă, utilizați filtrele avansate disponibile prin click pe linkul "Arată filtrele avansate" din formularul de căutare. Aici puteți specifica instanța judecătorească și intervalul de date pentru căutare.</p>

                        <h4 class="mt-3">Vizualizare detalii dosar</h4>
                        <p>După efectuarea unei căutări, puteți vizualiza detaliile complete ale unui dosar făcând click pe butonul "Detalii" din dreptul dosarului respectiv în lista de rezultate.</p>
                    </div>
                </div>

                <!-- Technologies Section -->
                <div class="card streamlined-card">
                    <div class="card-header">
                        <h3 class="h4 mb-0">
                            <i class="fas fa-code me-2"></i>
                            Tehnologii utilizate
                        </h3>
                    </div>
                    <div class="card-body about-section">
                        <div class="row">
                            <div class="col-md-6">
                                <h4>Backend</h4>
                                <ul>
                                    <li>PHP pentru procesarea datelor și interacțiunea cu API-ul SOAP</li>
                                    <li>Extensia SOAP din PHP pentru comunicarea cu serviciul web</li>
                                    <li>PhpSpreadsheet pentru export Excel</li>
                                    <li>TCPDF pentru generarea documentelor PDF</li>
                                </ul>
                            </div>
                            <div class="col-md-6">
                                <h4>Frontend</h4>
                                <ul>
                                    <li>HTML5 și CSS3 pentru structura și stilizarea paginilor</li>
                                    <li>Bootstrap 5 pentru design responsive</li>
                                    <li>JavaScript pentru interactivitate</li>
                                    <li>Font Awesome 6 pentru iconițe</li>
                                    <li>Flatpickr pentru selectarea datelor</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>

    <!-- Custom JavaScript -->
    <script>
        // Notification system matching bulk_search.php
        function showNotification(message, type = 'info') {
            const container = document.getElementById('notificationContainer');
            const notification = document.getElementById('notification');

            // Set the message and type
            notification.innerHTML = message;
            notification.className = `alert alert-${type}`;

            // Show the notification
            container.style.display = 'block';

            // Auto-hide after 5 seconds
            setTimeout(() => {
                container.style.display = 'none';
            }, 5000);
        }

        // Loading overlay functions
        function showLoading(message = 'Se încarcă...', submessage = 'Vă rugăm să așteptați...') {
            const overlay = document.getElementById('loadingOverlay');
            const messageEl = document.getElementById('loadingMessage');
            const submessageEl = document.getElementById('loadingSubmessage');

            if (messageEl) messageEl.textContent = message;
            if (submessageEl) submessageEl.textContent = submessage;

            overlay.style.display = 'flex';
        }

        function hideLoading() {
            const overlay = document.getElementById('loadingOverlay');
            overlay.style.display = 'none';
        }

        // Initialize page
        document.addEventListener('DOMContentLoaded', function() {
            // Any initialization code can go here
            console.log('About page loaded successfully');
        });
    </script>
</body>
</html>
