/**
 * Portal Judiciar - Funcționalitate de descărcare PDF
 */

document.addEventListener('DOMContentLoaded', function() {
    // Inițializăm funcționalitatea de descărcare PDF
    setupPdfDownload();
});

/**
 * Inițializează funcționalitatea de descărcare PDF
 */
function setupPdfDownload() {
    const downloadBtn = document.getElementById('downloadPdfBtn');

    if (downloadBtn) {
        downloadBtn.addEventListener('click', function() {
            // Obținem datele necesare pentru descărcare
            const numarDosar = this.getAttribute('data-numar');
            const institutie = this.getAttribute('data-institutie');
            const query = this.getAttribute('data-query');

            // Dezactivăm butonul și afișăm indicatorul de încărcare
            this.classList.add('loading');
            const originalText = this.innerHTML;
            this.innerHTML = '<span class="spinner-border spinner-border-sm me-1" role="status" aria-hidden="true"></span> Se generează...';

            // Construim URL-ul pentru descărcare
            let downloadUrl = 'export.php?format=pdf&direct=true';

            if (numarDosar && institutie) {
                // Descărcare pentru detalii dosar
                downloadUrl += '&numar=' + encodeURIComponent(numarDosar) + '&institutie=' + encodeURIComponent(institutie);
            } else if (query) {
                // Descărcare pentru rezultate căutare
                downloadUrl += '&query=' + encodeURIComponent(query);
            }

            // Afișăm notificare de informare
            showNotification('Se generează PDF-ul, vă rugăm așteptați...', 'info');

            // Creăm un iframe ascuns pentru a descărca fișierul
            const iframe = document.createElement('iframe');
            iframe.style.display = 'none';
            document.body.appendChild(iframe);

            // Setăm un timeout pentru a verifica dacă descărcarea a început
            const downloadTimeout = setTimeout(function() {
                // Restaurăm butonul
                downloadBtn.classList.remove('loading');
                downloadBtn.innerHTML = originalText;

                // Afișăm notificare de eroare
                showNotification('Generarea PDF-ului durează mai mult decât de obicei. Vă rugăm să încercați din nou.', 'danger');

                // Eliminăm iframe-ul
                if (iframe.parentNode) {
                    iframe.parentNode.removeChild(iframe);
                }
            }, 30000); // 30 secunde timeout

            // Setăm evenimentul de încărcare pentru iframe
            iframe.onload = function() {
                // Verificăm dacă iframe-ul a încărcat o pagină de eroare
                try {
                    const iframeContent = iframe.contentDocument || iframe.contentWindow.document;
                    const errorDiv = iframeContent.querySelector('div[style*="color: red"]');

                    if (errorDiv) {
                        // A apărut o eroare
                        clearTimeout(downloadTimeout);

                        // Restaurăm butonul
                        downloadBtn.classList.remove('loading');
                        downloadBtn.innerHTML = originalText;

                        // Afișăm notificare de eroare
                        showNotification('Eroare la generarea PDF-ului: ' + errorDiv.textContent.trim(), 'danger');

                        // Eliminăm iframe-ul
                        if (iframe.parentNode) {
                            iframe.parentNode.removeChild(iframe);
                        }

                        return;
                    }

                    // Verificăm tipul de conținut
                    const contentType = iframe.contentWindow.document.contentType || '';

                    if (contentType.includes('application/pdf')) {
                        // PDF-ul a fost generat cu succes
                        clearTimeout(downloadTimeout);

                        // Restaurăm butonul după o scurtă întârziere
                        setTimeout(function() {
                            downloadBtn.classList.remove('loading');
                            downloadBtn.innerHTML = originalText;

                            // Afișăm notificare de succes
                            showNotification('PDF-ul a fost generat și descărcat cu succes!', 'success');
                        }, 1000);
                    }
                } catch (e) {
                    // Probabil descărcarea a început, ceea ce a cauzat o eroare CORS
                    // Aceasta este comportamentul așteptat pentru descărcări
                    clearTimeout(downloadTimeout);

                    // Restaurăm butonul după o scurtă întârziere
                    setTimeout(function() {
                        downloadBtn.classList.remove('loading');
                        downloadBtn.innerHTML = originalText;

                        // Afișăm notificare de succes
                        showNotification('PDF-ul a fost generat și descărcat cu succes!', 'success');
                    }, 1000);
                }
            };

            // Setăm sursa iframe-ului pentru a începe descărcarea
            iframe.src = downloadUrl;
        });
    }
}

// Funcția showPdfNotification a fost înlocuită cu showNotification din main.js
