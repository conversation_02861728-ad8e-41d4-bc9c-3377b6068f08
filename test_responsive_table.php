<!DOCTYPE html>
<html lang="ro">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Responsive Table Layout</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    
    <style>
        :root {
            --primary-blue: #007bff;
            --secondary-blue: #2c3e50;
            --light-bg: #f8f9fa;
            --border-color: #dee2e6;
            --success-color: #28a745;
            --info-color: #17a2b8;
        }

        /* Enhanced Scale Table Layout - 35% Larger for Maximum Visibility */
        .table {
            margin-bottom: 0;
            font-size: 1.08rem; /* 0.8rem * 1.35 = 1.08rem */
            table-layout: fixed;
            width: 100%;
            border-collapse: collapse;
        }

        .table th {
            background-color: var(--light-bg);
            border-color: var(--border-color);
            color: var(--secondary-blue);
            font-weight: 600;
            padding: 0.675rem 0.405rem; /* 0.5rem * 1.35 = 0.675rem, 0.3rem * 1.35 = 0.405rem */
            word-wrap: break-word;
            overflow-wrap: break-word;
            hyphens: auto;
            line-height: 1.2;
            font-size: 1.0125rem; /* 0.75rem * 1.35 = 1.0125rem */
            text-align: center;
            vertical-align: middle;
        }

        .table td {
            padding: 0.675rem 0.405rem; /* 0.5rem * 1.35 = 0.675rem, 0.3rem * 1.35 = 0.405rem */
            vertical-align: top;
            border-color: var(--border-color);
            word-wrap: break-word;
            overflow-wrap: break-word;
            hyphens: auto;
            line-height: 1.3;
            font-size: 1.08rem; /* 0.8rem * 1.35 = 1.08rem */
            white-space: normal !important;
            text-align: left;
            max-height: none !important;
            overflow: visible !important;
        }

        /* Responsive table container - NO horizontal scroll */
        .table-responsive {
            overflow-x: visible;
            -webkit-overflow-scrolling: touch;
        }

        /* Ultra-Optimized Column Widths - Enhanced Date Display */
        .table th:nth-child(1), .table td:nth-child(1) { width: 10%; }
        .table th:nth-child(2), .table td:nth-child(2) { width: 8%; }
        .table th:nth-child(3), .table td:nth-child(3) { width: 24%; max-height: none; overflow: visible; }
        .table th:nth-child(4), .table td:nth-child(4) { width: 8%; }
        .table th:nth-child(5), .table td:nth-child(5) { width: 9%; text-align: center; }
        .table th:nth-child(6), .table td:nth-child(6) { width: 9%; text-align: center; }
        .table th:nth-child(7), .table td:nth-child(7) { width: 7%; }
        .table th:nth-child(8), .table td:nth-child(8) { width: 15%; max-height: none; overflow: visible; }
        .table th:nth-child(9), .table td:nth-child(9) { width: 7%; }
        .table th:nth-child(10), .table td:nth-child(10) { width: 9%; text-align: center; }

        /* Responsive Breakpoints */
        @media (max-width: 1200px) {
            .table { font-size: 0.8rem; }
            .table th, .table td { padding: 0.5rem 0.3rem; }
            .table th:nth-child(3), .table td:nth-child(3) { width: 22%; }
            .table th:nth-child(8), .table td:nth-child(8) { width: 15%; }
            .table th:nth-child(10), .table td:nth-child(10) { width: 11%; }
        }

        @media (max-width: 992px) {
            .table { font-size: 0.75rem; }
            .table th, .table td { padding: 0.4rem 0.25rem; line-height: 1.2; }
            .table th:nth-child(1), .table td:nth-child(1) { width: 12%; }
            .table th:nth-child(3), .table td:nth-child(3) { width: 21%; }
            .table th:nth-child(8), .table td:nth-child(8) { width: 15%; }
            .table th:nth-child(10), .table td:nth-child(10) { width: 12%; }
        }

        @media (max-width: 768px) {
            .table { font-size: 0.7rem; }
            .table th, .table td { padding: 0.3rem 0.2rem; line-height: 1.1; min-height: 2rem; }
            .table th:nth-child(1), .table td:nth-child(1) { width: 13%; }
            .table th:nth-child(2), .table td:nth-child(2) { width: 8%; }
            .table th:nth-child(3), .table td:nth-child(3) { width: 19%; }
            .table th:nth-child(5), .table td:nth-child(5) { width: 6%; }
            .table th:nth-child(6), .table td:nth-child(6) { width: 6%; }
            .table th:nth-child(8), .table td:nth-child(8) { width: 16%; }
            .table th:nth-child(10), .table td:nth-child(10) { width: 13%; }
        }

        @media (max-width: 576px) {
            .table { font-size: 0.65rem; }
            .table th, .table td { padding: 0.25rem 0.15rem; line-height: 1.1; }
            .table th:nth-child(1), .table td:nth-child(1) { width: 14%; }
            .table th:nth-child(2), .table td:nth-child(2) { width: 7%; }
            .table th:nth-child(3), .table td:nth-child(3) { width: 17%; }
            .table th:nth-child(5), .table td:nth-child(5) { width: 5%; }
            .table th:nth-child(6), .table td:nth-child(6) { width: 5%; }
            .table th:nth-child(7), .table td:nth-child(7) { width: 8%; }
            .table th:nth-child(8), .table td:nth-child(8) { width: 16%; }
            .table th:nth-child(9), .table td:nth-child(9) { width: 7%; }
            .table th:nth-child(10), .table td:nth-child(10) { width: 14%; }
        }

        /* Enhanced Party Matching Styles */
        .matching-party-name {
            display: inline-block;
            padding: 0.25rem 0.5rem;
            border-radius: 4px;
            transition: all 0.3s ease;
            word-wrap: break-word;
            overflow-wrap: break-word;
        }

        .matching-party-name.exact-phrase-match {
            background: linear-gradient(135deg, rgba(0, 123, 255, 0.2) 0%, rgba(23, 162, 184, 0.15) 100%);
            border-left: 4px solid var(--info-color);
            border-radius: 6px;
        }

        .matching-party-name.partial-match {
            background-color: rgba(0, 123, 255, 0.1);
            border-left: 3px solid var(--primary-blue);
        }

        .text-primary { color: var(--primary-blue) !important; font-weight: 700; }

        /* Enhanced Date Column Styling */
        .table th:nth-child(5), .table td:nth-child(5), /* Data */
        .table th:nth-child(6), .table td:nth-child(6) { /* Data ultimei modificări */
            text-align: center;
            white-space: nowrap;
            font-family: 'Courier New', monospace;
            font-weight: 500;
        }

        .table td:nth-child(5), .table td:nth-child(6) {
            font-size: 1.1475em; /* 0.85em * 1.35 = 1.1475em */
            letter-spacing: 0.675px; /* 0.5px * 1.35 = 0.675px */
        }
    </style>
</head>
<body>
    <div class="container-fluid mt-4">
        <h1 class="mb-4">Test Responsive Table Layout</h1>
        
        <div class="alert alert-info">
            <i class="fas fa-info-circle me-2"></i>
            <strong>Test Instructions:</strong> Resize your browser window to test responsiveness. 
            All 10 columns should remain visible without horizontal scrolling at any screen size.
        </div>

        <div class="table-responsive">
            <table class="table table-striped table-hover">
                <thead>
                    <tr>
                        <th>Număr Dosar</th>
                        <th>Instanță</th>
                        <th>Obiect</th>
                        <th>Stadiu Procesual</th>
                        <th>Data</th>
                        <th>Data ultimei modificări</th>
                        <th>Categorie caz</th>
                        <th>Nume Parte</th>
                        <th>Calitate</th>
                        <th>Acțiuni</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td>12748/211/2019</td>
                        <td>Judecătoria Sector 1</td>
                        <td>Acțiune în răspundere civilă delictuală pentru repararea prejudiciului cauzat prin accident de circulație</td>
                        <td>Soluționat definitiv</td>
                        <td>15.03.2019</td>
                        <td>22.11.2019</td>
                        <td>Civil</td>
                        <td><span class="matching-party-name exact-phrase-match" title="✓ Potrivire exactă de frază"><strong class="text-primary">MAIER RADU MIHAI</strong></span></td>
                        <td>Pârât</td>
                        <td><button class="btn btn-primary btn-sm"><i class="fas fa-eye"></i></button></td>
                    </tr>
                    <tr>
                        <td>5432/102/2020</td>
                        <td>Tribunalul București</td>
                        <td>Cerere de chemare în judecată pentru încetarea contractului de închiriere și evacuare</td>
                        <td>În curs de judecată</td>
                        <td>08.07.2020</td>
                        <td>15.01.2024</td>
                        <td>Civil</td>
                        <td><span class="matching-party-name partial-match" title="✓ Potrivire parțială">IONESCU <strong class="text-primary">MARIA</strong> ELENA</span></td>
                        <td>Reclamant</td>
                        <td><button class="btn btn-primary btn-sm"><i class="fas fa-eye"></i></button></td>
                    </tr>
                    <tr>
                        <td>9876/543/2021</td>
                        <td>Curtea de Apel București</td>
                        <td>Apel împotriva sentinței civile nr. 123/2021 pronunțată de Tribunalul București în dosarul nr. 1234/2020</td>
                        <td>Soluționat definitiv</td>
                        <td>12.09.2021</td>
                        <td>03.12.2021</td>
                        <td>Civil</td>
                        <td>SC EXEMPLU CONSTRUCT SRL</td>
                        <td>Apelant</td>
                        <td><button class="btn btn-primary btn-sm"><i class="fas fa-eye"></i></button></td>
                    </tr>
                    <tr>
                        <td>1111/222/2023</td>
                        <td>Judecătoria Sector 2</td>
                        <td>Acțiune în constatarea nulității absolute a contractului de vânzare-cumpărare și revendicarea imobilului</td>
                        <td>În curs de judecată</td>
                        <td>25.04.2023</td>
                        <td>18.01.2024</td>
                        <td>Civil</td>
                        <td>POPESCU ION ALEXANDRU</td>
                        <td>Reclamant</td>
                        <td><button class="btn btn-primary btn-sm"><i class="fas fa-eye"></i></button></td>
                    </tr>
                </tbody>
            </table>
        </div>

        <div class="mt-4">
            <h3>Responsive Breakpoints Test</h3>
            <div class="row">
                <div class="col-md-3">
                    <div class="card">
                        <div class="card-body">
                            <h6>Desktop (≥1200px)</h6>
                            <small>Font: 0.85rem, Full spacing</small>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card">
                        <div class="card-body">
                            <h6>Large Tablet (992-1199px)</h6>
                            <small>Font: 0.8rem, Reduced spacing</small>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card">
                        <div class="card-body">
                            <h6>Tablet (768-991px)</h6>
                            <small>Font: 0.75rem, Compact layout</small>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card">
                        <div class="card-body">
                            <h6>Mobile (≤767px)</h6>
                            <small>Font: 0.7rem, Ultra compact</small>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="mt-4">
            <a href="bulk_search.php" class="btn btn-primary">
                <i class="fas fa-arrow-left me-2"></i>
                Back to Bulk Search
            </a>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
