<?php
/**
 * Debug script specifically for MAIER RADU case investigation
 */

// Include necessary files
require_once 'includes/config.php';
require_once 'services/DosarService.php';

echo "<h1>MAIER RADU Case Investigation</h1>";
echo "<style>
    .debug-section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; background: #f9f9f9; }
    .party-item { margin: 10px 0; padding: 10px; border-left: 3px solid #007bff; background: #f8f9fa; }
    .match { background: #d4edda !important; border-left-color: #28a745 !important; }
    .no-match { background: #f8d7da !important; border-left-color: #dc3545 !important; }
</style>";

try {
    // Initialize the service
    $dosarService = new DosarService();
    
    // Search for MAIER RADU
    $searchParams = [
        'numarDosar' => '',
        'institutie' => null,
        'obiectDosar' => '',
        'numeParte' => 'MAIER RADU',
        'dataStart' => null,
        'dataStop' => null,
        'dataUltimaModificareStart' => null,
        'dataUltimaModificareStop' => null
    ];
    
    echo "<div class='debug-section'>";
    echo "<h2>Search Parameters</h2>";
    echo "<pre>" . print_r($searchParams, true) . "</pre>";
    echo "</div>";
    
    echo "<div class='debug-section'>";
    echo "<h2>Executing Search...</h2>";
    $results = $dosarService->cautareAvansata($searchParams);
    echo "<p><strong>Total Results:</strong> " . count($results) . "</p>";
    echo "</div>";
    
    // Look for case 12748/211/2019
    $targetCase = null;
    foreach ($results as $dosar) {
        if (($dosar->numar ?? '') === '12748/211/2019') {
            $targetCase = $dosar;
            break;
        }
    }
    
    if ($targetCase) {
        echo "<div class='debug-section'>";
        echo "<h2>✅ Target Case Found: 12748/211/2019</h2>";
        
        echo "<h3>Case Details:</h3>";
        echo "<ul>";
        echo "<li><strong>Număr:</strong> " . htmlspecialchars($targetCase->numar ?? '') . "</li>";
        echo "<li><strong>Instanță:</strong> " . htmlspecialchars($targetCase->institutie ?? '') . "</li>";
        echo "<li><strong>Obiect:</strong> " . htmlspecialchars($targetCase->obiect ?? '') . "</li>";
        echo "<li><strong>Data:</strong> " . htmlspecialchars($targetCase->data ?? '') . "</li>";
        echo "</ul>";
        
        // Analyze parties
        echo "<h3>Party Analysis:</h3>";
        $parti = $targetCase->parti ?? [];
        
        if (is_array($parti) && !empty($parti)) {
            echo "<p><strong>Parties Count:</strong> " . count($parti) . "</p>";
            echo "<p><strong>Parties Data Type:</strong> " . gettype($parti) . "</p>";
            
            // Include the enhanced matching functions
            include_once 'bulk_search_functions.php';
            
            echo "<h4>Individual Party Analysis:</h4>";
            foreach ($parti as $index => $party) {
                $nume = '';
                $calitate = '';
                
                if (is_array($party)) {
                    $nume = $party['nume'] ?? '';
                    $calitate = $party['calitate'] ?? '';
                } elseif (is_object($party)) {
                    $nume = $party->nume ?? '';
                    $calitate = $party->calitate ?? '';
                }
                
                // Test matching
                $searchTerm = 'MAIER RADU';
                $normalizedPartyName = mb_strtolower(trim($nume), 'UTF-8');
                $normalizedSearchTerm = mb_strtolower(trim($searchTerm), 'UTF-8');
                $isMatch = mb_strpos($normalizedPartyName, $normalizedSearchTerm, 0, 'UTF-8') !== false;
                
                $cssClass = $isMatch ? 'party-item match' : 'party-item no-match';
                $matchText = $isMatch ? "✅ MATCH" : "❌ No match";
                
                echo "<div class='{$cssClass}'>";
                echo "<strong>Party {$index}:</strong><br>";
                echo "<strong>Name:</strong> " . htmlspecialchars($nume) . "<br>";
                echo "<strong>Quality:</strong> " . htmlspecialchars($calitate) . "<br>";
                echo "<strong>Normalized Name:</strong> " . htmlspecialchars($normalizedPartyName) . "<br>";
                echo "<strong>Match Status:</strong> {$matchText}<br>";
                echo "<strong>Raw Data:</strong> <pre>" . print_r($party, true) . "</pre>";
                echo "</div>";
            }
            
            // Test enhanced matching functions
            if (function_exists('findMatchingParty')) {
                echo "<h4>Enhanced Function Tests:</h4>";
                
                echo "<div class='debug-section'>";
                echo "<h5>findMatchingParty() Test:</h5>";
                $matchingParty = findMatchingParty($parti, 'MAIER RADU');
                if ($matchingParty) {
                    echo "<p><strong>✅ Matching Party Found:</strong></p>";
                    echo "<pre>" . print_r($matchingParty, true) . "</pre>";
                } else {
                    echo "<p><strong>❌ No matching party found</strong></p>";
                }
                echo "</div>";
                
                echo "<div class='debug-section'>";
                echo "<h5>getRelevantPartyName() Test:</h5>";
                $relevantName = getRelevantPartyName($parti, 'MAIER RADU', 'numeParte');
                echo "<p><strong>Result:</strong> '" . htmlspecialchars($relevantName) . "'</p>";
                echo "</div>";
                
                echo "<div class='debug-section'>";
                echo "<h5>First Party (Fallback) Test:</h5>";
                $firstParty = reset($parti);
                if ($firstParty) {
                    $firstName = is_array($firstParty) ? ($firstParty['nume'] ?? '') : '';
                    echo "<p><strong>First Party Name:</strong> '" . htmlspecialchars($firstName) . "'</p>";
                    echo "<p><strong>First Party Data:</strong></p>";
                    echo "<pre>" . print_r($firstParty, true) . "</pre>";
                }
                echo "</div>";
                
                echo "<div class='debug-section'>";
                echo "<h5>highlightMatchingPartyName() Test:</h5>";
                if (!empty($relevantName)) {
                    $highlighted = highlightMatchingPartyName($relevantName, 'MAIER RADU', 'numeParte');
                    echo "<p><strong>Highlighted Result:</strong> {$highlighted}</p>";
                }
                echo "</div>";
            }
            
        } else {
            echo "<p><strong>❌ No parties found or invalid data structure</strong></p>";
            echo "<p><strong>Parties Data Type:</strong> " . gettype($parti) . "</p>";
            echo "<p><strong>Parties Data:</strong></p>";
            echo "<pre>" . print_r($parti, true) . "</pre>";
        }
        echo "</div>";
        
    } else {
        echo "<div class='debug-section'>";
        echo "<h2>❌ Target Case NOT Found: 12748/211/2019</h2>";
        
        if (!empty($results)) {
            echo "<h3>Available Cases (first 10):</h3>";
            echo "<ul>";
            foreach (array_slice($results, 0, 10) as $dosar) {
                echo "<li>" . htmlspecialchars($dosar->numar ?? 'Unknown') . " - " . htmlspecialchars($dosar->institutie ?? 'Unknown') . "</li>";
            }
            echo "</ul>";
        } else {
            echo "<p>No results found for search term 'MAIER RADU'</p>";
        }
        echo "</div>";
    }
    
} catch (Exception $e) {
    echo "<div class='debug-section'>";
    echo "<h2>❌ Error during search:</h2>";
    echo "<p>" . htmlspecialchars($e->getMessage()) . "</p>";
    echo "</div>";
}

echo "<hr>";
echo "<p><a href='bulk_search.php'>← Back to Bulk Search</a></p>";
?>
