<?php
/**
 * Comprehensive debugging script for future date range filtering issues
 */

require_once 'includes/config.php';
require_once 'includes/functions.php';
require_once 'src/Services/DosarService.php';

echo "<h1>🔍 Future Date Range Filtering Debug</h1>\n";
echo "<style>
    .debug-section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
    .success { background-color: #d4edda; border-color: #c3e6cb; }
    .error { background-color: #f8d7da; border-color: #f5c6cb; }
    .warning { background-color: #fff3cd; border-color: #ffeaa7; }
    .info { background-color: #d1ecf1; border-color: #bee5eb; }
    pre { background: #f8f9fa; padding: 10px; border-radius: 3px; overflow-x: auto; max-height: 400px; overflow-y: auto; }
    .test-result { margin: 5px 0; padding: 8px; border-radius: 3px; }
    .pass { background-color: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
    .fail { background-color: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
    .skip { background-color: #fff3cd; color: #856404; border: 1px solid #ffeaa7; }
    .test-url { background: #f8f9fa; padding: 10px; border-radius: 3px; margin: 10px 0; border-left: 4px solid #007bff; }
</style>\n";

echo "<div class='debug-section info'>";
echo "<h2>🎯 Debugging Future Date Range Issue</h2>\n";
echo "<p><strong>Problem:</strong> Search with date range extending to 2025 not returning expected results</p>";
echo "<p><strong>Test Case:</strong> Creation dates 01.06.2023 - 15.06.2025 + multiple filters</p>";
echo "</div>";

// Test the specific failing parameters
$failingParams = [
    'institutie' => 'CurteadeApelBUCURESTI',
    'categorieInstanta' => 'curte_apel',
    'categorieCaz' => 'munca',
    'dataStart' => '01.06.2023',
    'dataStop' => '15.06.2025',
    'dataUltimaModificareStart' => '01.01.2023',
    'dataUltimaModificareStop' => '31.12.2023',
    'sortBy' => 'data',
    'sortDirection' => 'desc',
    '_maxResults' => 500,
    'page' => 1
];

echo "<div class='debug-section'>";
echo "<h2>Test 1: Date Validation and Conversion Analysis</h2>\n";

echo "<h4>1.1 Date Range Validation</h4>\n";

// Test the date validation function
function validateRomanianDate($dateString, $fieldName) {
    if (empty($dateString)) {
        return null;
    }
    
    if (!preg_match('/^\d{1,2}\.\d{1,2}\.\d{4}$/', $dateString)) {
        return "Formatul datei pentru '{$fieldName}' trebuie să fie ZZ.LL.AAAA (ex: 12.06.2023)";
    }
    
    $timestamp = strtotime($dateString);
    if ($timestamp === false) {
        return "Data '{$dateString}' pentru '{$fieldName}' nu este validă";
    }
    
    $maxFutureTime = strtotime('+5 years');
    if ($timestamp > $maxFutureTime) {
        return "Data '{$dateString}' pentru '{$fieldName}' este prea departe în viitor";
    }
    
    return null;
}

$dateFields = [
    'dataStart' => 'Data început',
    'dataStop' => 'Data sfârșit',
    'dataUltimaModificareStart' => 'Data modificării început',
    'dataUltimaModificareStop' => 'Data modificării sfârșit'
];

foreach ($dateFields as $field => $label) {
    if (!empty($failingParams[$field])) {
        $error = validateRomanianDate($failingParams[$field], $label);
        if ($error) {
            echo "<div class='test-result fail'>";
            echo "❌ {$label}: {$error}";
            echo "</div>";
        } else {
            echo "<div class='test-result pass'>";
            echo "✅ {$label}: Valid date ({$failingParams[$field]})";
            echo "</div>";
        }
    }
}

// Test date range validation
$startTs = strtotime($failingParams['dataStart']);
$stopTs = strtotime($failingParams['dataStop']);

if ($startTs && $stopTs) {
    $rangeValid = ($startTs <= $stopTs);
    $daysDiff = ($stopTs - $startTs) / (24 * 60 * 60);
    
    echo "<div class='test-result " . ($rangeValid ? 'pass' : 'fail') . "'>";
    echo "Date range validation: " . ($rangeValid ? '✅ Valid' : '❌ Invalid');
    echo " (Range: " . round($daysDiff) . " days)";
    echo "</div>";
}

echo "<h4>1.2 SOAP Date Format Conversion Test</h4>\n";

try {
    $dosarService = new \App\Services\DosarService();
    
    // Test date conversion using reflection to access private method
    $reflection = new ReflectionClass($dosarService);
    $formatMethod = $reflection->getMethod('formatDateForSoap');
    $formatMethod->setAccessible(true);
    
    foreach (['dataStart', 'dataStop'] as $field) {
        if (!empty($failingParams[$field])) {
            $originalDate = $failingParams[$field];
            $convertedDate = $formatMethod->invoke($dosarService, $originalDate);
            
            echo "<div class='test-result " . ($convertedDate ? 'pass' : 'fail') . "'>";
            echo "'{$originalDate}' => '" . ($convertedDate ?: 'NULL') . "'";
            echo "</div>";
        }
    }
    
} catch (Exception $e) {
    echo "<div class='test-result fail'>";
    echo "❌ Error testing date conversion: " . htmlspecialchars($e->getMessage());
    echo "</div>";
}

echo "</div>";

// Test 2: Isolated Date Range Tests
echo "<div class='debug-section'>";
echo "<h2>Test 2: Isolated Date Range Tests</h2>\n";

$isolatedTests = [
    [
        'name' => '2024 Only',
        'params' => [
            'dataStart' => '01.01.2024',
            'dataStop' => '31.12.2024',
            '_maxResults' => 10
        ]
    ],
    [
        'name' => '2025 Only',
        'params' => [
            'dataStart' => '01.01.2025',
            'dataStop' => '31.12.2025',
            '_maxResults' => 10
        ]
    ],
    [
        'name' => 'Extended Range (2023-2025)',
        'params' => [
            'dataStart' => '01.06.2023',
            'dataStop' => '15.06.2025',
            '_maxResults' => 10
        ]
    ],
    [
        'name' => 'Current Year Only',
        'params' => [
            'dataStart' => '01.01.2023',
            'dataStop' => '31.12.2023',
            '_maxResults' => 10
        ]
    ]
];

foreach ($isolatedTests as $test) {
    echo "<h4>2." . (array_search($test, $isolatedTests) + 1) . " Testing: {$test['name']}</h4>\n";
    
    // Prepare full parameters for SOAP call
    $testParams = array_merge([
        'numarDosar' => '',
        'numeParte' => '',
        'obiectDosar' => '',
        'institutie' => null,
        'dataStart' => '',
        'dataStop' => '',
        'dataUltimaModificareStart' => '',
        'dataUltimaModificareStop' => '',
    ], $test['params']);
    
    echo "<div class='test-url'>";
    echo "<strong>Test Parameters:</strong><br>";
    echo "dataStart: " . ($testParams['dataStart'] ?: 'empty') . "<br>";
    echo "dataStop: " . ($testParams['dataStop'] ?: 'empty') . "<br>";
    echo "maxResults: " . $testParams['_maxResults'];
    echo "</div>";
    
    try {
        $searchStart = microtime(true);
        $results = $dosarService->cautareAvansata($testParams);
        $searchDuration = microtime(true) - $searchStart;
        
        echo "<div class='test-result " . (count($results) > 0 ? 'pass' : 'warning') . "'>";
        echo "Results: " . count($results) . " cases found in " . round($searchDuration, 3) . "s";
        echo "</div>";
        
        if (!empty($results)) {
            // Analyze date distribution
            $dateDistribution = [];
            foreach ($results as $result) {
                $date = $result->data ?? '';
                if (!empty($date)) {
                    $year = date('Y', strtotime($date));
                    $dateDistribution[$year] = ($dateDistribution[$year] ?? 0) + 1;
                }
            }
            
            if (!empty($dateDistribution)) {
                ksort($dateDistribution);
                echo "<div class='test-result info'>";
                echo "Date distribution by year: ";
                foreach ($dateDistribution as $year => $count) {
                    echo "{$year}: {$count} cases; ";
                }
                echo "</div>";
            }
            
            // Show sample results
            echo "<h5>Sample results (first 3):</h5>";
            for ($i = 0; $i < min(3, count($results)); $i++) {
                $result = $results[$i];
                echo "<div class='test-result info'>";
                echo "Case: " . htmlspecialchars($result->numar ?? 'N/A');
                echo " | Date: " . htmlspecialchars($result->data ?? 'N/A');
                echo " | Institution: " . htmlspecialchars($result->institutie ?? 'N/A');
                echo "</div>";
            }
        }
        
    } catch (Exception $e) {
        echo "<div class='test-result fail'>";
        echo "❌ Search failed: " . htmlspecialchars($e->getMessage());
        echo "</div>";
    }
}

echo "</div>";

// Test 3: Full Parameter Testing with Client-Side Filtering Analysis
echo "<div class='debug-section'>";
echo "<h2>Test 3: Full Parameter Testing with Filtering Analysis</h2>\n";

echo "<h4>3.1 Complete Search with All Parameters</h4>\n";

// Prepare the full failing parameters for SOAP call
$fullParams = [
    'numarDosar' => '',
    'numeParte' => '',
    'obiectDosar' => '',
    'institutie' => $failingParams['institutie'],
    'categorieInstanta' => $failingParams['categorieInstanta'],
    'categorieCaz' => $failingParams['categorieCaz'],
    'dataStart' => $failingParams['dataStart'],
    'dataStop' => $failingParams['dataStop'],
    'dataUltimaModificareStart' => $failingParams['dataUltimaModificareStart'],
    'dataUltimaModificareStop' => $failingParams['dataUltimaModificareStop'],
    '_maxResults' => $failingParams['_maxResults']
];

echo "<div class='test-url'>";
echo "<strong>Full Test Parameters:</strong><br>";
echo "<pre>" . print_r($fullParams, true) . "</pre>";
echo "</div>";

try {
    echo "<h5>Step 1: SOAP API Call (Before Client-Side Filtering)</h5>\n";

    // First, test with just the SOAP-supported parameters (no client-side filtering)
    $soapOnlyParams = [
        'numarDosar' => $fullParams['numarDosar'],
        'numeParte' => $fullParams['numeParte'],
        'obiectDosar' => $fullParams['obiectDosar'],
        'institutie' => $fullParams['institutie'],
        'dataStart' => $fullParams['dataStart'],
        'dataStop' => $fullParams['dataStop'],
        'dataUltimaModificareStart' => $fullParams['dataUltimaModificareStart'],
        'dataUltimaModificareStop' => $fullParams['dataUltimaModificareStop'],
        '_maxResults' => $fullParams['_maxResults']
    ];

    $searchStart = microtime(true);
    $rawResults = $dosarService->cautareAvansata($soapOnlyParams);
    $searchDuration = microtime(true) - $searchStart;

    echo "<div class='test-result " . (count($rawResults) > 0 ? 'pass' : 'warning') . "'>";
    echo "SOAP API Raw Results: " . count($rawResults) . " cases in " . round($searchDuration, 3) . "s";
    echo "</div>";

    if (!empty($rawResults)) {
        // Analyze raw results by year
        $rawDateDistribution = [];
        foreach ($rawResults as $result) {
            $date = $result->data ?? '';
            if (!empty($date)) {
                $year = date('Y', strtotime($date));
                $rawDateDistribution[$year] = ($rawDateDistribution[$year] ?? 0) + 1;
            }
        }

        if (!empty($rawDateDistribution)) {
            ksort($rawDateDistribution);
            echo "<div class='test-result info'>";
            echo "Raw results by year: ";
            foreach ($rawDateDistribution as $year => $count) {
                echo "{$year}: {$count} cases; ";
            }
            echo "</div>";
        }

        echo "<h5>Step 2: Client-Side Filtering Analysis</h5>\n";

        // Now test the full search with client-side filtering
        $filteredResults = $dosarService->cautareAvansata($fullParams);

        echo "<div class='test-result " . (count($filteredResults) > 0 ? 'pass' : 'warning') . "'>";
        echo "After Client-Side Filtering: " . count($filteredResults) . " cases";
        echo " (Filtered out: " . (count($rawResults) - count($filteredResults)) . " cases)";
        echo "</div>";

        if (count($rawResults) !== count($filteredResults)) {
            echo "<div class='test-result warning'>";
            echo "⚠️ Client-side filtering removed " . (count($rawResults) - count($filteredResults)) . " results";
            echo "</div>";

            // Analyze what was filtered out
            if (!empty($filteredResults)) {
                $filteredDateDistribution = [];
                foreach ($filteredResults as $result) {
                    $date = $result->data ?? '';
                    if (!empty($date)) {
                        $year = date('Y', strtotime($date));
                        $filteredDateDistribution[$year] = ($filteredDateDistribution[$year] ?? 0) + 1;
                    }
                }

                if (!empty($filteredDateDistribution)) {
                    ksort($filteredDateDistribution);
                    echo "<div class='test-result info'>";
                    echo "Filtered results by year: ";
                    foreach ($filteredDateDistribution as $year => $count) {
                        echo "{$year}: {$count} cases; ";
                    }
                    echo "</div>";

                    // Compare distributions
                    foreach ($rawDateDistribution as $year => $rawCount) {
                        $filteredCount = $filteredDateDistribution[$year] ?? 0;
                        if ($rawCount !== $filteredCount) {
                            echo "<div class='test-result warning'>";
                            echo "⚠️ Year {$year}: {$rawCount} raw → {$filteredCount} filtered (lost " . ($rawCount - $filteredCount) . ")";
                            echo "</div>";
                        }
                    }
                }
            }
        }

        // Show sample filtered results
        if (!empty($filteredResults)) {
            echo "<h5>Sample filtered results (first 3):</h5>";
            for ($i = 0; $i < min(3, count($filteredResults)); $i++) {
                $result = $filteredResults[$i];
                echo "<div class='test-result info'>";
                echo "Case: " . htmlspecialchars($result->numar ?? 'N/A');
                echo " | Date: " . htmlspecialchars($result->data ?? 'N/A');
                echo " | Institution: " . htmlspecialchars($result->institutie ?? 'N/A');
                echo " | Object: " . htmlspecialchars(substr($result->obiect ?? 'N/A', 0, 50)) . "...";
                echo "</div>";
            }
        }
    } else {
        echo "<div class='test-result warning'>";
        echo "⚠️ No raw results from SOAP API - issue may be with SOAP parameters or database content";
        echo "</div>";
    }

} catch (Exception $e) {
    echo "<div class='test-result fail'>";
    echo "❌ Full search failed: " . htmlspecialchars($e->getMessage());
    echo "</div>";
}

echo "</div>";

// Test 4: SOAP Parameter Analysis
echo "<div class='debug-section'>";
echo "<h2>Test 4: SOAP Parameter Analysis</h2>\n";

echo "<h4>4.1 Actual SOAP Parameters Being Sent</h4>\n";

// Function to format date for SOAP (replicate the logic from DosarService)
function debugFormatDateForSoap($date) {
    if (empty($date)) {
        return null;
    }

    // Încercăm mai multe formate posibile pentru data românească
    $formats = [
        'd.m.Y',    // 31.12.2023
        'j.n.Y',    // 1.1.2023 (fără zero-uri)
        'd.n.Y',    // 31.1.2023
        'j.m.Y'     // 1.12.2023
    ];

    foreach ($formats as $format) {
        $dateObj = \DateTime::createFromFormat($format, $date);
        if ($dateObj && $dateObj->format($format) == $date) {
            return $dateObj->format('Y-m-d\TH:i:s');
        }
    }

    // Fallback: încercăm strtotime
    $timestamp = strtotime($date);
    if ($timestamp !== false) {
        return date('Y-m-d\TH:i:s', $timestamp);
    }

    return null;
}

try {
    // Format SOAP parameters manually
    $soapParams = [
        'numarDosar' => $fullParams['numarDosar'] ?? '',
        'obiectDosar' => $fullParams['obiectDosar'] ?? '',
        'numeParte' => $fullParams['numeParte'] ?? '',
        'institutie' => $fullParams['institutie'] ?? null,
        'dataStart' => debugFormatDateForSoap($fullParams['dataStart'] ?? ''),
        'dataStop' => debugFormatDateForSoap($fullParams['dataStop'] ?? ''),
        'dataUltimaModificareStart' => debugFormatDateForSoap($fullParams['dataUltimaModificareStart'] ?? ''),
        'dataUltimaModificareStop' => debugFormatDateForSoap($fullParams['dataUltimaModificareStop'] ?? '')
    ];

    echo "<div class='test-result info'>";
    echo "<strong>Formatted SOAP Parameters:</strong>";
    echo "<pre>" . print_r($soapParams, true) . "</pre>";
    echo "</div>";

    // Check if future dates are properly formatted
    foreach (['dataStart', 'dataStop', 'dataUltimaModificareStart', 'dataUltimaModificareStop'] as $field) {
        if (!empty($soapParams[$field])) {
            $originalDate = $fullParams[$field];
            $soapDate = $soapParams[$field];

            echo "<div class='test-result pass'>";
            echo "✅ {$field}: '{$originalDate}' → '{$soapDate}'";
            echo "</div>";
        }
    }

} catch (Exception $e) {
    echo "<div class='test-result fail'>";
    echo "❌ Error analyzing SOAP parameters: " . htmlspecialchars($e->getMessage());
    echo "</div>";
}

echo "</div>";

echo "<div class='debug-section info'>";
echo "<h2>🏁 Comprehensive Future Date Debug Complete</h2>\n";
echo "<p><strong>Key Analysis Points:</strong></p>";
echo "<ul>";
echo "<li>✅ Date validation and conversion testing</li>";
echo "<li>✅ Isolated date range testing (2024, 2025, extended ranges)</li>";
echo "<li>✅ Raw SOAP API results vs. client-side filtered results</li>";
echo "<li>✅ Year-by-year result distribution analysis</li>";
echo "<li>✅ SOAP parameter formatting verification</li>";
echo "</ul>";
echo "<p><strong>Next step:</strong> Review results to identify the root cause of missing 2024/2025 results.</p>";
echo "</div>";
